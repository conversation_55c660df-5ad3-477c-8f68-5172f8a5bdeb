"""
Custom middleware for the FastAPI application.
"""

import time
import logging
from datetime import datetime
from typing import Dict, Any
from fastapi import Request, Response, HTTPException
from fastapi.responses import JSONResponse
import asyncio

from ..utils.config import get_config


logger = logging.getLogger(__name__)


# Rate limiting storage (in production, use Redis)
rate_limit_storage: Dict[str, Dict[str, Any]] = {}


async def rate_limit_middleware(request: Request, call_next):
    """Rate limiting middleware."""
    config = get_config()
    
    # Skip rate limiting for health checks and docs
    if request.url.path in ["/health", "/docs", "/redoc", "/openapi.json"]:
        return await call_next(request)
    
    # Get client IP
    client_ip = request.client.host
    current_time = time.time()
    
    # Parse rate limit (e.g., "100/minute")
    rate_limit = config.api.rate_limit
    try:
        limit, period = rate_limit.split("/")
        limit = int(limit)
        
        if period == "minute":
            window = 60
        elif period == "hour":
            window = 3600
        elif period == "day":
            window = 86400
        else:
            window = 60  # Default to minute
    except:
        limit = 100
        window = 60
    
    # Check rate limit
    if client_ip not in rate_limit_storage:
        rate_limit_storage[client_ip] = {
            "requests": [],
            "blocked_until": 0
        }
    
    client_data = rate_limit_storage[client_ip]
    
    # Check if client is currently blocked
    if current_time < client_data["blocked_until"]:
        raise HTTPException(
            status_code=429,
            detail="Rate limit exceeded. Please try again later."
        )
    
    # Clean old requests
    client_data["requests"] = [
        req_time for req_time in client_data["requests"]
        if current_time - req_time < window
    ]
    
    # Check if limit exceeded
    if len(client_data["requests"]) >= limit:
        client_data["blocked_until"] = current_time + window
        raise HTTPException(
            status_code=429,
            detail=f"Rate limit exceeded. Maximum {limit} requests per {period}."
        )
    
    # Add current request
    client_data["requests"].append(current_time)
    
    # Process request
    response = await call_next(request)
    
    # Add rate limit headers
    remaining = max(0, limit - len(client_data["requests"]))
    response.headers["X-RateLimit-Limit"] = str(limit)
    response.headers["X-RateLimit-Remaining"] = str(remaining)
    response.headers["X-RateLimit-Reset"] = str(int(current_time + window))
    
    return response


async def error_handler_middleware(request: Request, call_next):
    """Global error handling middleware."""
    start_time = time.time()
    
    try:
        response = await call_next(request)
        
        # Log successful requests
        process_time = time.time() - start_time
        logger.info(
            f"{request.method} {request.url.path} - "
            f"Status: {response.status_code} - "
            f"Time: {process_time:.3f}s"
        )
        
        return response
        
    except HTTPException as e:
        # Handle HTTP exceptions
        process_time = time.time() - start_time
        logger.warning(
            f"{request.method} {request.url.path} - "
            f"HTTP Error: {e.status_code} - {e.detail} - "
            f"Time: {process_time:.3f}s"
        )
        
        return JSONResponse(
            status_code=e.status_code,
            content={
                "error": "HTTP Error",
                "message": e.detail,
                "status_code": e.status_code,
                "timestamp": datetime.now().isoformat(),
                "path": request.url.path
            }
        )
        
    except Exception as e:
        # Handle unexpected errors
        process_time = time.time() - start_time
        logger.error(
            f"{request.method} {request.url.path} - "
            f"Internal Error: {str(e)} - "
            f"Time: {process_time:.3f}s",
            exc_info=True
        )
        
        return JSONResponse(
            status_code=500,
            content={
                "error": "Internal Server Error",
                "message": "An unexpected error occurred",
                "timestamp": datetime.now().isoformat(),
                "path": request.url.path
            }
        )


async def security_middleware(request: Request, call_next):
    """Security middleware for API key validation."""
    config = get_config()
    
    # Skip security for public endpoints
    public_endpoints = ["/health", "/docs", "/redoc", "/openapi.json", "/"]
    if request.url.path in public_endpoints:
        return await call_next(request)
    
    # Check if API key is required
    if not config.security.api_key_required:
        return await call_next(request)
    
    # Validate API key
    api_key = request.headers.get(config.security.api_key_header)
    if not api_key:
        raise HTTPException(
            status_code=401,
            detail=f"Missing API key in header: {config.security.api_key_header}"
        )
    
    # TODO: Implement actual API key validation
    # For now, just check if it's not empty
    if not api_key.strip():
        raise HTTPException(
            status_code=401,
            detail="Invalid API key"
        )
    
    return await call_next(request)


async def cors_middleware(request: Request, call_next):
    """Custom CORS middleware."""
    config = get_config()
    
    response = await call_next(request)
    
    if config.api.cors_enabled:
        # Add CORS headers
        origin = request.headers.get("origin")
        if origin and (
            "*" in config.api.cors_origins or 
            origin in config.api.cors_origins
        ):
            response.headers["Access-Control-Allow-Origin"] = origin
            response.headers["Access-Control-Allow-Credentials"] = "true"
            response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
            response.headers["Access-Control-Allow-Headers"] = "*"
    
    return response


async def logging_middleware(request: Request, call_next):
    """Request logging middleware."""
    start_time = time.time()
    
    # Log request
    logger.info(
        f"Request: {request.method} {request.url.path} - "
        f"Client: {request.client.host} - "
        f"User-Agent: {request.headers.get('user-agent', 'Unknown')}"
    )
    
    response = await call_next(request)
    
    # Log response
    process_time = time.time() - start_time
    logger.info(
        f"Response: {request.method} {request.url.path} - "
        f"Status: {response.status_code} - "
        f"Time: {process_time:.3f}s"
    )
    
    # Add timing header
    response.headers["X-Process-Time"] = str(process_time)
    
    return response

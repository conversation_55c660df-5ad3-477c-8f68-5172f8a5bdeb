"""
Unit tests for video downloaders.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from pathlib import Path

from video_downloader.downloaders.base import BaseDownloader, ProgressCallback
from video_downloader.downloaders.youtube import YouTubeDownloader
from video_downloader.downloaders.tiktok import TikTokDownloader
from video_downloader.downloaders.factory import DownloaderFactory
from video_downloader.api.models import (
    DownloadRequest, VideoMetadata, Platform, VideoQuality, 
    AudioFormat, VideoFormat
)
from video_downloader.utils.config import Config


class TestBaseDownloader:
    """Test base downloader functionality."""
    
    def test_sanitize_filename(self):
        """Test filename sanitization."""
        config = Mock(spec=Config)
        downloader = BaseDownloader(config)
        
        # Test invalid characters
        assert downloader._sanitize_filename("test<>file") == "test__file"
        assert downloader._sanitize_filename("test/file") == "test_file"
        assert downloader._sanitize_filename("test|file") == "test_file"
        
        # Test length limit
        long_name = "a" * 250
        sanitized = downloader._sanitize_filename(long_name)
        assert len(sanitized) <= 200
        
        # Test empty filename
        assert downloader._sanitize_filename("") == "video"
        assert downloader._sanitize_filename("   ") == "video"
    
    def test_parse_url(self):
        """Test URL parsing."""
        config = Mock(spec=Config)
        downloader = BaseDownloader(config)
        
        url = "https://www.youtube.com/watch?v=test123"
        parsed = downloader._parse_url(url)
        
        assert parsed['scheme'] == 'https'
        assert parsed['domain'] == 'www.youtube.com'
        assert parsed['path'] == '/watch'
        assert parsed['query'] == 'v=test123'
        assert parsed['full_url'] == url


class TestYouTubeDownloader:
    """Test YouTube downloader."""
    
    @pytest.fixture
    def config(self):
        """Create mock config."""
        config = Mock(spec=Config)
        config.storage.download_dir = "./downloads"
        config.storage.temp_dir = "./temp"
        config.storage.organize_by_platform = True
        config.storage.organize_by_date = False
        config.processing.use_cpp_engine = False
        config.processing.ffmpeg_path = "ffmpeg"
        return config
    
    @pytest.fixture
    def downloader(self, config):
        """Create YouTube downloader."""
        return YouTubeDownloader(config)
    
    def test_can_handle_youtube_urls(self, downloader):
        """Test YouTube URL detection."""
        youtube_urls = [
            "https://www.youtube.com/watch?v=test123",
            "https://youtu.be/test123",
            "https://m.youtube.com/watch?v=test123",
            "https://music.youtube.com/watch?v=test123"
        ]
        
        for url in youtube_urls:
            assert downloader.can_handle(url)
        
        # Test non-YouTube URLs
        non_youtube_urls = [
            "https://www.tiktok.com/@user/video/123",
            "https://www.facebook.com/video/123",
            "https://www.example.com/video"
        ]
        
        for url in non_youtube_urls:
            assert not downloader.can_handle(url)
    
    @pytest.mark.asyncio
    async def test_extract_metadata_mock(self, downloader):
        """Test metadata extraction with mocked yt-dlp."""
        mock_info = {
            'title': 'Test Video',
            'description': 'Test description',
            'duration': 120,
            'uploader': 'Test Channel',
            'upload_date': '20231201',
            'view_count': 1000,
            'like_count': 50,
            'thumbnail': 'https://example.com/thumb.jpg',
            'id': 'test123',
            'tags': ['test', 'video'],
            'categories': ['Entertainment']
        }
        
        with patch('yt_dlp.YoutubeDL') as mock_ydl:
            mock_ydl.return_value.__enter__.return_value.extract_info.return_value = mock_info
            
            metadata = await downloader.extract_metadata("https://www.youtube.com/watch?v=test123")
            
            assert metadata.title == "Test Video"
            assert metadata.platform == Platform.YOUTUBE
            assert metadata.video_id == "test123"
            assert metadata.duration == 120
            assert "test" in metadata.tags
    
    def test_get_format_selector(self, downloader):
        """Test format selector generation."""
        # Test best quality
        selector = downloader._get_format_selector(VideoQuality.BEST, VideoFormat.MP4)
        assert "best[ext=mp4]" in selector
        
        # Test specific quality
        selector = downloader._get_format_selector(VideoQuality.Q1080P, VideoFormat.MP4)
        assert "height<=1080" in selector
        
        # Test audio only
        selector = downloader._get_format_selector(VideoQuality.AUDIO_ONLY, VideoFormat.MP4)
        assert "bestaudio" in selector


class TestTikTokDownloader:
    """Test TikTok downloader."""
    
    @pytest.fixture
    def config(self):
        """Create mock config."""
        config = Mock(spec=Config)
        config.storage.download_dir = "./downloads"
        config.storage.temp_dir = "./temp"
        config.storage.organize_by_platform = True
        config.storage.organize_by_date = False
        config.processing.use_cpp_engine = False
        config.processing.ffmpeg_path = "ffmpeg"
        return config
    
    @pytest.fixture
    def downloader(self, config):
        """Create TikTok downloader."""
        return TikTokDownloader(config)
    
    def test_can_handle_tiktok_urls(self, downloader):
        """Test TikTok URL detection."""
        tiktok_urls = [
            "https://www.tiktok.com/@user/video/123",
            "https://vm.tiktok.com/abc123",
            "https://m.tiktok.com/@user/video/123"
        ]
        
        for url in tiktok_urls:
            assert downloader.can_handle(url)
    
    def test_extract_video_id(self, downloader):
        """Test video ID extraction."""
        urls_and_ids = [
            ("https://www.tiktok.com/@user/video/1234567890", "1234567890"),
            ("https://tiktok.com/@user/video/9876543210", "9876543210"),
            ("https://vm.tiktok.com/shortlink", None)  # Short URLs don't have video ID in URL
        ]
        
        for url, expected_id in urls_and_ids:
            result = downloader._extract_video_id(url)
            assert result == expected_id
    
    def test_extract_hashtags(self, downloader):
        """Test hashtag extraction."""
        description = "Check out this amazing video! #fyp #viral #trending #dance"
        hashtags = downloader._extract_hashtags(description)
        
        expected_hashtags = ["fyp", "viral", "trending", "dance"]
        assert hashtags == expected_hashtags
        
        # Test empty description
        assert downloader._extract_hashtags("") == []
        assert downloader._extract_hashtags(None) == []


class TestDownloaderFactory:
    """Test downloader factory."""
    
    @pytest.fixture
    def factory(self):
        """Create downloader factory."""
        return DownloaderFactory()
    
    def test_get_downloader_for_youtube(self, factory):
        """Test getting YouTube downloader."""
        with patch.object(factory.config, 'is_platform_enabled', return_value=True):
            factory.initialize()
            
            downloader = factory.get_downloader("https://www.youtube.com/watch?v=test123")
            assert isinstance(downloader, YouTubeDownloader)
    
    def test_get_downloader_for_tiktok(self, factory):
        """Test getting TikTok downloader."""
        with patch.object(factory.config, 'is_platform_enabled', return_value=True):
            factory.initialize()
            
            downloader = factory.get_downloader("https://www.tiktok.com/@user/video/123")
            assert isinstance(downloader, TikTokDownloader)
    
    def test_get_downloader_for_unsupported_url(self, factory):
        """Test getting downloader for unsupported URL."""
        with patch.object(factory.config, 'is_platform_enabled', return_value=True):
            factory.initialize()
            
            downloader = factory.get_downloader("https://www.example.com/video")
            assert downloader is None
    
    def test_validate_request(self, factory):
        """Test request validation."""
        with patch.object(factory.config, 'is_platform_enabled', return_value=True):
            factory.initialize()
            
            # Valid request
            request = DownloadRequest(
                url="https://www.youtube.com/watch?v=test123",
                quality=VideoQuality.Q1080P,
                video_format=VideoFormat.MP4
            )
            assert factory.validate_request(request)
            
            # Invalid request
            request = DownloadRequest(
                url="https://www.example.com/video",
                quality=VideoQuality.Q1080P,
                video_format=VideoFormat.MP4
            )
            assert not factory.validate_request(request)


class TestProgressCallback:
    """Test progress callback functionality."""
    
    def test_progress_callback_creation(self):
        """Test progress callback creation."""
        callback_called = False
        
        async def mock_update(progress):
            nonlocal callback_called
            callback_called = True
            assert progress.job_id == "test-job"
            assert progress.progress_percent == 50.0
        
        callback = ProgressCallback("test-job", mock_update)
        
        progress_data = {
            'progress_percent': 50.0,
            'downloaded_bytes': 1024,
            'total_bytes': 2048,
            'current_stage': 'downloading'
        }
        
        callback(progress_data)
        
        # Give async task time to run
        import time
        time.sleep(0.1)
        
        assert callback_called


@pytest.mark.integration
class TestDownloadIntegration:
    """Integration tests for downloaders."""
    
    @pytest.mark.asyncio
    async def test_youtube_download_integration(self):
        """Test actual YouTube download (requires network)."""
        # This test requires a real YouTube URL and network access
        # Skip in CI/CD environments
        pytest.skip("Integration test requires network access")
        
        config = Mock(spec=Config)
        config.storage.download_dir = "./test_downloads"
        config.storage.temp_dir = "./test_temp"
        config.storage.organize_by_platform = False
        config.storage.organize_by_date = False
        config.processing.use_cpp_engine = False
        
        downloader = YouTubeDownloader(config)
        
        # Use a short, public domain video for testing
        test_url = "https://www.youtube.com/watch?v=jNQXAC9IVRw"  # "Me at the zoo" - first YouTube video
        
        try:
            metadata = await downloader.extract_metadata(test_url)
            assert metadata.title is not None
            assert metadata.platform == Platform.YOUTUBE
            
            # Test getting available qualities
            qualities = await downloader.get_available_qualities(test_url)
            assert len(qualities) > 0
            assert VideoQuality.BEST in qualities
            
        except Exception as e:
            pytest.skip(f"Integration test failed due to network/API issues: {e}")


if __name__ == "__main__":
    pytest.main([__file__])

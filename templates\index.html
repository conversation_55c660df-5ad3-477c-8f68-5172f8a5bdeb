{% extends "base.html" %}

{% block title %}Home - Video Downloader Service{% endblock %}

{% block content %}
<div class="stats">
    <div class="stat-card">
        <div class="stat-number">{{ queue_size }}</div>
        <div class="stat-label">Queued Downloads</div>
    </div>
    <div class="stat-card">
        <div class="stat-number">{{ active_jobs }}</div>
        <div class="stat-label">Active Downloads</div>
    </div>
    <div class="stat-card">
        <div class="stat-number">{{ recent_jobs|length }}</div>
        <div class="stat-label">Recent Completed</div>
    </div>
</div>

<div class="card">
    <h2>Download Video</h2>
    <form action="/web/download" method="post">
        <div class="form-group">
            <label for="url">Video URL *</label>
            <input type="url" id="url" name="url" required 
                   placeholder="https://www.youtube.com/watch?v=example">
            <small>Supported platforms: YouTube, TikTok, Facebook</small>
        </div>
        
        <div class="form-group">
            <label for="quality">Video Quality</label>
            <select id="quality" name="quality">
                {% for quality in qualities %}
                <option value="{{ quality }}" {% if quality == 'best' %}selected{% endif %}>
                    {{ quality.title() }}
                </option>
                {% endfor %}
            </select>
        </div>
        
        <div class="form-group">
            <div class="checkbox-item">
                <input type="checkbox" id="audio_only" name="audio_only" value="true">
                <label for="audio_only">Audio Only</label>
            </div>
        </div>
        
        <div class="form-group">
            <label for="audio_format">Audio Format</label>
            <select id="audio_format" name="audio_format">
                {% for format in audio_formats %}
                <option value="{{ format }}" {% if format == 'mp3' %}selected{% endif %}>
                    {{ format.upper() }}
                </option>
                {% endfor %}
            </select>
        </div>
        
        <div class="form-group">
            <label for="video_format">Video Format</label>
            <select id="video_format" name="video_format">
                {% for format in video_formats %}
                <option value="{{ format }}" {% if format == 'mp4' %}selected{% endif %}>
                    {{ format.upper() }}
                </option>
                {% endfor %}
            </select>
        </div>
        
        <div class="form-group">
            <div class="checkbox-group">
                <div class="checkbox-item">
                    <input type="checkbox" id="extract_thumbnails" name="extract_thumbnails" 
                           value="true" checked>
                    <label for="extract_thumbnails">Extract Thumbnails</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="extract_subtitles" name="extract_subtitles" 
                           value="true">
                    <label for="extract_subtitles">Extract Subtitles (YouTube)</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="remove_watermark" name="remove_watermark" 
                           value="true" checked>
                    <label for="remove_watermark">Remove Watermark (TikTok)</label>
                </div>
            </div>
        </div>
        
        <button type="submit" class="btn btn-success">Download</button>
    </form>
</div>

{% if recent_jobs %}
<div class="card">
    <h2>Recent Downloads</h2>
    <div style="overflow-x: auto;">
        <table style="width: 100%; border-collapse: collapse;">
            <thead>
                <tr style="border-bottom: 2px solid #ecf0f1;">
                    <th style="text-align: left; padding: 10px;">Status</th>
                    <th style="text-align: left; padding: 10px;">URL</th>
                    <th style="text-align: left; padding: 10px;">Created</th>
                    <th style="text-align: left; padding: 10px;">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for job in recent_jobs %}
                <tr style="border-bottom: 1px solid #ecf0f1;">
                    <td style="padding: 10px;">
                        <span class="status status-{{ job.status.value }}">
                            {{ job.status.value }}
                        </span>
                    </td>
                    <td style="padding: 10px;">
                        <div style="max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                            {{ job.request.url }}
                        </div>
                    </td>
                    <td style="padding: 10px;">
                        {{ job.created_at.strftime('%Y-%m-%d %H:%M') }}
                    </td>
                    <td style="padding: 10px;">
                        <a href="/web/status/{{ job.id }}" class="btn" style="font-size: 12px; padding: 5px 10px;">
                            View
                        </a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    <div style="margin-top: 15px;">
        <a href="/web/jobs" class="btn">View All Jobs</a>
    </div>
</div>
{% endif %}

<div class="card">
    <h2>Quick Start</h2>
    <ol>
        <li>Paste a video URL from YouTube, TikTok, or Facebook</li>
        <li>Choose your preferred quality and format settings</li>
        <li>Click "Download" to start the process</li>
        <li>Monitor progress on the status page</li>
        <li>Download completed files from the results</li>
    </ol>
    
    <h3>Supported Platforms</h3>
    <ul>
        <li><strong>YouTube:</strong> Videos, playlists, live streams, subtitles</li>
        <li><strong>TikTok:</strong> Videos with watermark removal option</li>
        <li><strong>Facebook:</strong> Public videos and posts</li>
    </ul>
    
    <h3>API Access</h3>
    <p>For programmatic access, use our REST API:</p>
    <ul>
        <li><a href="/docs">Interactive API Documentation (Swagger)</a></li>
        <li><a href="/redoc">Alternative API Documentation (ReDoc)</a></li>
        <li><a href="/web/api-docs">Simple API Guide</a></li>
    </ul>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    // Auto-refresh stats every 30 seconds
    setInterval(function() {
        fetch('/web/api/stats')
            .then(response => response.json())
            .then(data => {
                // Update stats if endpoint exists
                // This is a placeholder for future implementation
            })
            .catch(error => console.log('Stats update failed:', error));
    }, 30000);
    
    // Form validation and UX improvements
    document.addEventListener('DOMContentLoaded', function() {
        const audioOnlyCheckbox = document.getElementById('audio_only');
        const videoFormatSelect = document.getElementById('video_format');
        const qualitySelect = document.getElementById('quality');
        
        audioOnlyCheckbox.addEventListener('change', function() {
            if (this.checked) {
                videoFormatSelect.disabled = true;
                qualitySelect.value = 'best';
                qualitySelect.disabled = true;
            } else {
                videoFormatSelect.disabled = false;
                qualitySelect.disabled = false;
            }
        });
        
        // URL validation
        const urlInput = document.getElementById('url');
        urlInput.addEventListener('input', function() {
            const url = this.value;
            const supportedDomains = ['youtube.com', 'youtu.be', 'tiktok.com', 'facebook.com', 'fb.watch'];
            const isSupported = supportedDomains.some(domain => url.includes(domain));
            
            if (url && !isSupported) {
                this.setCustomValidity('Please enter a URL from YouTube, TikTok, or Facebook');
            } else {
                this.setCustomValidity('');
            }
        });
    });
</script>
{% endblock %}

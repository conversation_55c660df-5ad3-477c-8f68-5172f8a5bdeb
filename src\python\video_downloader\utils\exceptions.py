"""
Custom exceptions for the video downloader service.
"""

from typing import Optional, Dict, Any


class VideoDownloaderError(Exception):
    """Base exception for video downloader errors."""
    
    def __init__(self, message: str, error_code: Optional[str] = None, 
                 details: Optional[Dict[str, Any]] = None):
        """Initialize the exception."""
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary."""
        return {
            'error_type': self.__class__.__name__,
            'error_code': self.error_code,
            'message': self.message,
            'details': self.details
        }


class ConfigurationError(VideoDownloaderError):
    """Raised when there's a configuration error."""
    pass


class ServiceError(VideoDownloaderError):
    """Raised when there's a service-level error."""
    pass


class DownloadError(VideoDownloaderError):
    """Base exception for download-related errors."""
    pass


class UnsupportedPlatformError(DownloadError):
    """Raised when a platform is not supported."""
    
    def __init__(self, platform: str, url: str):
        """Initialize the exception."""
        message = f"Platform not supported: {platform}"
        details = {'platform': platform, 'url': url}
        super().__init__(message, details=details)


class VideoNotFoundError(DownloadError):
    """Raised when a video is not found or unavailable."""
    
    def __init__(self, url: str, reason: Optional[str] = None):
        """Initialize the exception."""
        message = f"Video not found or unavailable: {url}"
        if reason:
            message += f" ({reason})"
        details = {'url': url, 'reason': reason}
        super().__init__(message, details=details)


class QualityNotAvailableError(DownloadError):
    """Raised when requested quality is not available."""
    
    def __init__(self, requested_quality: str, available_qualities: list):
        """Initialize the exception."""
        message = f"Quality '{requested_quality}' not available. Available: {', '.join(available_qualities)}"
        details = {
            'requested_quality': requested_quality,
            'available_qualities': available_qualities
        }
        super().__init__(message, details=details)


class NetworkError(DownloadError):
    """Raised when there's a network-related error."""
    
    def __init__(self, message: str, status_code: Optional[int] = None, 
                 url: Optional[str] = None):
        """Initialize the exception."""
        details = {}
        if status_code:
            details['status_code'] = status_code
        if url:
            details['url'] = url
        super().__init__(message, details=details)


class AuthenticationError(DownloadError):
    """Raised when authentication fails."""
    
    def __init__(self, platform: str, reason: Optional[str] = None):
        """Initialize the exception."""
        message = f"Authentication failed for {platform}"
        if reason:
            message += f": {reason}"
        details = {'platform': platform, 'reason': reason}
        super().__init__(message, details=details)


class RateLimitError(DownloadError):
    """Raised when rate limit is exceeded."""
    
    def __init__(self, platform: str, retry_after: Optional[int] = None):
        """Initialize the exception."""
        message = f"Rate limit exceeded for {platform}"
        if retry_after:
            message += f". Retry after {retry_after} seconds"
        details = {'platform': platform, 'retry_after': retry_after}
        super().__init__(message, details=details)


class ProcessingError(VideoDownloaderError):
    """Raised when there's an error during video processing."""
    
    def __init__(self, operation: str, reason: str, file_path: Optional[str] = None):
        """Initialize the exception."""
        message = f"Processing error during {operation}: {reason}"
        details = {'operation': operation, 'reason': reason}
        if file_path:
            details['file_path'] = file_path
        super().__init__(message, details=details)


class StorageError(VideoDownloaderError):
    """Raised when there's a storage-related error."""
    
    def __init__(self, operation: str, path: str, reason: str):
        """Initialize the exception."""
        message = f"Storage error during {operation} at {path}: {reason}"
        details = {'operation': operation, 'path': path, 'reason': reason}
        super().__init__(message, details=details)


class QueueError(VideoDownloaderError):
    """Raised when there's a queue-related error."""
    pass


class JobNotFoundError(QueueError):
    """Raised when a job is not found in the queue."""
    
    def __init__(self, job_id: str):
        """Initialize the exception."""
        message = f"Job not found: {job_id}"
        details = {'job_id': job_id}
        super().__init__(message, details=details)


class JobStateError(QueueError):
    """Raised when a job is in an invalid state for the requested operation."""
    
    def __init__(self, job_id: str, current_state: str, required_state: str):
        """Initialize the exception."""
        message = f"Job {job_id} is in state '{current_state}', required '{required_state}'"
        details = {
            'job_id': job_id,
            'current_state': current_state,
            'required_state': required_state
        }
        super().__init__(message, details=details)


class APIError(VideoDownloaderError):
    """Raised when there's an API-related error."""
    
    def __init__(self, message: str, status_code: int = 500, 
                 endpoint: Optional[str] = None):
        """Initialize the exception."""
        details = {'status_code': status_code}
        if endpoint:
            details['endpoint'] = endpoint
        super().__init__(message, details=details)


class ValidationError(APIError):
    """Raised when request validation fails."""
    
    def __init__(self, field: str, value: Any, reason: str):
        """Initialize the exception."""
        message = f"Validation error for field '{field}': {reason}"
        details = {'field': field, 'value': str(value), 'reason': reason}
        super().__init__(message, status_code=400, details=details)


class ResourceNotFoundError(APIError):
    """Raised when a requested resource is not found."""
    
    def __init__(self, resource_type: str, resource_id: str):
        """Initialize the exception."""
        message = f"{resource_type} not found: {resource_id}"
        details = {'resource_type': resource_type, 'resource_id': resource_id}
        super().__init__(message, status_code=404, details=details)


class ConflictError(APIError):
    """Raised when there's a conflict with the current state."""
    
    def __init__(self, message: str, conflicting_resource: Optional[str] = None):
        """Initialize the exception."""
        details = {}
        if conflicting_resource:
            details['conflicting_resource'] = conflicting_resource
        super().__init__(message, status_code=409, details=details)


class TooManyRequestsError(APIError):
    """Raised when rate limit is exceeded."""
    
    def __init__(self, retry_after: Optional[int] = None):
        """Initialize the exception."""
        message = "Too many requests"
        if retry_after:
            message += f". Retry after {retry_after} seconds"
        details = {'retry_after': retry_after}
        super().__init__(message, status_code=429, details=details)


# Exception mapping for HTTP status codes
HTTP_EXCEPTION_MAP = {
    400: ValidationError,
    404: ResourceNotFoundError,
    409: ConflictError,
    429: TooManyRequestsError,
}


def create_http_exception(status_code: int, message: str, **kwargs) -> APIError:
    """Create an appropriate HTTP exception based on status code."""
    exception_class = HTTP_EXCEPTION_MAP.get(status_code, APIError)
    
    if exception_class == ValidationError:
        # ValidationError requires specific parameters
        return ValidationError(
            field=kwargs.get('field', 'unknown'),
            value=kwargs.get('value', ''),
            reason=message
        )
    elif exception_class == ResourceNotFoundError:
        # ResourceNotFoundError requires specific parameters
        return ResourceNotFoundError(
            resource_type=kwargs.get('resource_type', 'resource'),
            resource_id=kwargs.get('resource_id', 'unknown')
        )
    elif exception_class == TooManyRequestsError:
        return TooManyRequestsError(retry_after=kwargs.get('retry_after'))
    else:
        return exception_class(message, **kwargs)


class ErrorHandler:
    """Centralized error handling and logging."""
    
    def __init__(self, logger=None):
        """Initialize error handler."""
        self.logger = logger or __import__('logging').getLogger(__name__)
    
    def handle_exception(self, exc: Exception, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Handle an exception and return error details."""
        context = context or {}
        
        if isinstance(exc, VideoDownloaderError):
            # Our custom exceptions
            error_details = exc.to_dict()
            error_details.update(context)
            
            # Log based on error type
            if isinstance(exc, (NetworkError, RateLimitError)):
                self.logger.warning(f"Network/Rate limit error: {exc.message}", extra=error_details)
            elif isinstance(exc, (ValidationError, UnsupportedPlatformError)):
                self.logger.info(f"Validation error: {exc.message}", extra=error_details)
            else:
                self.logger.error(f"Download error: {exc.message}", extra=error_details)
            
            return error_details
        
        else:
            # Unexpected exceptions
            error_details = {
                'error_type': 'UnexpectedError',
                'error_code': 'UNEXPECTED_ERROR',
                'message': str(exc),
                'details': context
            }
            
            self.logger.error(f"Unexpected error: {exc}", exc_info=True, extra=error_details)
            return error_details
    
    def create_error_response(self, exc: Exception, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Create a standardized error response."""
        error_details = self.handle_exception(exc, context)
        
        return {
            'success': False,
            'error': error_details,
            'timestamp': __import__('datetime').datetime.now().isoformat()
        }


# Global error handler instance
_error_handler: Optional[ErrorHandler] = None


def get_error_handler() -> ErrorHandler:
    """Get the global error handler instance."""
    global _error_handler
    if _error_handler is None:
        _error_handler = ErrorHandler()
    return _error_handler

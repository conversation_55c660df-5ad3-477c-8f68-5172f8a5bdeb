"""
Unit tests for the API endpoints.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from fastapi.testclient import TestClient
from datetime import datetime

from video_downloader.api.app import create_app
from video_downloader.api.models import (
    DownloadRequest, JobStatus, VideoQuality, AudioFormat, VideoFormat
)
from video_downloader.queue.job_queue import Job


@pytest.fixture
def client():
    """Create test client."""
    app = create_app()
    return TestClient(app)


@pytest.fixture
def mock_queue_manager():
    """Create mock queue manager."""
    manager = Mock()
    manager.submit_job = AsyncMock(return_value="test-job-123")
    manager.get_job_status = AsyncMock()
    manager.cancel_job = AsyncMock(return_value=True)
    manager.retry_job = AsyncMock(return_value=True)
    manager.get_queue_stats = AsyncMock(return_value={
        "queue_size": 5,
        "active_jobs": 2,
        "active_job_ids": ["job1", "job2"]
    })
    return manager


@pytest.fixture
def mock_downloader_factory():
    """Create mock downloader factory."""
    factory = Mock()
    factory.validate_request = Mock(return_value=True)
    factory.get_supported_platforms = Mock(return_value=[])
    factory.get_platform_capabilities = Mock(return_value={})
    return factory


class TestDownloadEndpoints:
    """Test download API endpoints."""
    
    def test_health_endpoint(self, client):
        """Test health check endpoint."""
        response = client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "version" in data
    
    def test_root_endpoint(self, client):
        """Test root endpoint."""
        response = client.get("/")
        assert response.status_code == 200
        
        data = response.json()
        assert "message" in data
        assert "version" in data
        assert "docs" in data
    
    @patch('video_downloader.api.routes.download.get_job_queue_manager')
    @patch('video_downloader.api.routes.download.get_downloader_factory')
    def test_submit_download(self, mock_factory_getter, mock_queue_getter, client):
        """Test download submission."""
        # Setup mocks
        mock_queue_getter.return_value = Mock()
        mock_queue_getter.return_value.submit_job = AsyncMock(return_value="test-job-123")
        mock_queue_getter.return_value.get_queue_stats = AsyncMock(return_value={"queue_size": 0})
        
        mock_factory_getter.return_value = Mock()
        mock_factory_getter.return_value.validate_request = Mock(return_value=True)
        
        # Test valid request
        request_data = {
            "url": "https://www.youtube.com/watch?v=test123",
            "quality": "1080p",
            "audio_only": False,
            "audio_format": "mp3",
            "video_format": "mp4"
        }
        
        response = client.post("/api/download", json=request_data)
        assert response.status_code == 200
        
        data = response.json()
        assert data["job_id"] == "test-job-123"
        assert data["status"] == "pending"
        assert "message" in data
    
    def test_submit_download_invalid_url(self, client):
        """Test download submission with invalid URL."""
        request_data = {
            "url": "not-a-valid-url",
            "quality": "1080p"
        }
        
        response = client.post("/api/download", json=request_data)
        assert response.status_code == 422  # Validation error
    
    @patch('video_downloader.api.routes.download.get_job_queue_manager')
    @patch('video_downloader.api.routes.download.get_downloader_factory')
    def test_batch_download(self, mock_factory_getter, mock_queue_getter, client):
        """Test batch download submission."""
        # Setup mocks
        mock_queue_getter.return_value = Mock()
        mock_queue_getter.return_value.submit_job = AsyncMock(side_effect=["job1", "job2"])
        
        mock_factory_getter.return_value = Mock()
        mock_factory_getter.return_value.validate_request = Mock(return_value=True)
        
        request_data = {
            "urls": [
                "https://www.youtube.com/watch?v=test1",
                "https://www.youtube.com/watch?v=test2"
            ],
            "quality": "720p"
        }
        
        response = client.post("/api/download/batch", json=request_data)
        assert response.status_code == 200
        
        data = response.json()
        assert len(data["job_ids"]) == 2
        assert data["total_jobs"] == 2
        assert "batch_id" in data
    
    def test_batch_download_too_many_urls(self, client):
        """Test batch download with too many URLs."""
        request_data = {
            "urls": ["https://example.com"] * 101,  # More than 100 URLs
            "quality": "720p"
        }
        
        response = client.post("/api/download/batch", json=request_data)
        assert response.status_code == 400
    
    @patch('video_downloader.api.routes.download.get_job_queue_manager')
    def test_cancel_download(self, mock_queue_getter, client):
        """Test download cancellation."""
        mock_queue_getter.return_value = Mock()
        mock_queue_getter.return_value.cancel_job = AsyncMock(return_value=True)
        
        response = client.delete("/api/download/test-job-123")
        assert response.status_code == 200
        
        data = response.json()
        assert "cancelled successfully" in data["message"]
    
    @patch('video_downloader.api.routes.download.get_job_queue_manager')
    def test_cancel_download_not_found(self, mock_queue_getter, client):
        """Test cancelling non-existent download."""
        mock_queue_getter.return_value = Mock()
        mock_queue_getter.return_value.cancel_job = AsyncMock(return_value=False)
        
        response = client.delete("/api/download/nonexistent-job")
        assert response.status_code == 404
    
    @patch('video_downloader.api.routes.download.get_job_queue_manager')
    def test_retry_download(self, mock_queue_getter, client):
        """Test download retry."""
        mock_queue_getter.return_value = Mock()
        mock_queue_getter.return_value.retry_job = AsyncMock(return_value=True)
        
        response = client.post("/api/download/test-job-123/retry")
        assert response.status_code == 200
        
        data = response.json()
        assert "scheduled for retry" in data["message"]
    
    @patch('video_downloader.api.routes.download.get_job_queue_manager')
    def test_get_queue_stats(self, mock_queue_getter, client):
        """Test getting queue statistics."""
        mock_queue_getter.return_value = Mock()
        mock_queue_getter.return_value.get_queue_stats = AsyncMock(return_value={
            "queue_size": 10,
            "active_jobs": 3,
            "active_job_ids": ["job1", "job2", "job3"]
        })
        
        response = client.get("/api/download/queue/stats")
        assert response.status_code == 200
        
        data = response.json()
        assert data["queue_size"] == 10
        assert data["active_jobs"] == 3
        assert len(data["active_job_ids"]) == 3
    
    @patch('video_downloader.api.routes.download.get_downloader_factory')
    def test_get_supported_platforms(self, mock_factory_getter, client):
        """Test getting supported platforms."""
        from video_downloader.api.models import Platform
        
        mock_factory_getter.return_value = Mock()
        mock_factory_getter.return_value.get_supported_platforms = Mock(
            return_value=[Platform.YOUTUBE, Platform.TIKTOK]
        )
        mock_factory_getter.return_value.get_platform_capabilities = Mock(
            return_value={"enabled": True, "max_quality": "4K"}
        )
        
        response = client.get("/api/download/platforms")
        assert response.status_code == 200
        
        data = response.json()
        assert "supported_platforms" in data
        assert "platform_capabilities" in data


class TestStatusEndpoints:
    """Test status API endpoints."""
    
    @patch('video_downloader.api.routes.status.get_job_queue_manager')
    def test_get_job_status(self, mock_queue_getter, client):
        """Test getting job status."""
        # Create mock job
        mock_job = Mock()
        mock_job.id = "test-job-123"
        mock_job.status = JobStatus.DOWNLOADING
        mock_job.progress = None
        mock_job.result = None
        
        mock_queue_getter.return_value = Mock()
        mock_queue_getter.return_value.get_job_status = AsyncMock(return_value=mock_job)
        
        response = client.get("/api/status/test-job-123")
        assert response.status_code == 200
        
        data = response.json()
        assert data["job_id"] == "test-job-123"
        assert data["status"] == "downloading"
    
    @patch('video_downloader.api.routes.status.get_job_queue_manager')
    def test_get_job_status_not_found(self, mock_queue_getter, client):
        """Test getting status for non-existent job."""
        mock_queue_getter.return_value = Mock()
        mock_queue_getter.return_value.get_job_status = AsyncMock(return_value=None)
        
        response = client.get("/api/status/nonexistent-job")
        assert response.status_code == 404
    
    @patch('video_downloader.api.routes.status.get_job_queue_manager')
    def test_list_downloads(self, mock_queue_getter, client):
        """Test listing downloads."""
        # Create mock jobs
        mock_jobs = []
        for i in range(5):
            job = Mock()
            job.id = f"job-{i}"
            job.status = JobStatus.COMPLETED
            job.result = {"file_path": f"/path/to/file{i}.mp4"}
            mock_jobs.append(job)
        
        mock_queue_getter.return_value = Mock()
        mock_queue_getter.return_value.queue.get_completed_jobs = AsyncMock(return_value=mock_jobs)
        
        response = client.get("/api/downloads")
        assert response.status_code == 200
        
        data = response.json()
        assert "downloads" in data
        assert "total_count" in data
        assert "page" in data
        assert "page_size" in data
    
    def test_list_downloads_with_pagination(self, client):
        """Test listing downloads with pagination parameters."""
        response = client.get("/api/downloads?page=2&page_size=10")
        assert response.status_code == 200
        
        data = response.json()
        assert data["page"] == 2
        assert data["page_size"] == 10
    
    @patch('video_downloader.api.routes.status.get_job_queue_manager')
    def test_get_active_jobs(self, mock_queue_getter, client):
        """Test getting active jobs."""
        # Create mock active jobs
        mock_jobs = []
        for i in range(3):
            job = Mock()
            job.id = f"active-job-{i}"
            job.status = JobStatus.DOWNLOADING
            job.request = Mock()
            job.request.url = f"https://example.com/video{i}"
            job.created_at = datetime.now()
            job.progress = None
            mock_jobs.append(job)
        
        mock_queue_getter.return_value = Mock()
        mock_queue_getter.return_value.queue.get_active_jobs = AsyncMock(return_value=mock_jobs)
        
        response = client.get("/api/jobs/active")
        assert response.status_code == 200
        
        data = response.json()
        assert "active_jobs" in data
        assert "count" in data
        assert data["count"] == 3
    
    @patch('video_downloader.api.routes.status.get_job_queue_manager')
    def test_get_recent_jobs(self, mock_queue_getter, client):
        """Test getting recent jobs."""
        mock_jobs = []
        for i in range(5):
            job = Mock()
            job.id = f"recent-job-{i}"
            job.status = JobStatus.COMPLETED
            job.request = Mock()
            job.request.url = f"https://example.com/video{i}"
            job.created_at = datetime.now()
            job.updated_at = datetime.now()
            job.error_message = None
            job.result = {"file_path": f"/path/to/file{i}.mp4"}
            mock_jobs.append(job)
        
        mock_queue_getter.return_value = Mock()
        mock_queue_getter.return_value.queue.get_completed_jobs = AsyncMock(return_value=mock_jobs)
        
        response = client.get("/api/jobs/recent?limit=5")
        assert response.status_code == 200
        
        data = response.json()
        assert "recent_jobs" in data
        assert "count" in data
        assert data["count"] == 5


class TestWebInterface:
    """Test web interface endpoints."""
    
    def test_web_interface_loads(self, client):
        """Test that web interface loads."""
        # This might fail if templates are not found, but should not crash
        response = client.get("/web")
        # Accept either 200 (success) or 500 (template error) - both mean the endpoint exists
        assert response.status_code in [200, 500]
    
    def test_web_api_docs(self, client):
        """Test web API documentation page."""
        response = client.get("/web/api-docs")
        assert response.status_code == 200
        assert "text/html" in response.headers["content-type"]


if __name__ == "__main__":
    pytest.main([__file__])

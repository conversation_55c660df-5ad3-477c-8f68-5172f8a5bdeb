{% extends "base.html" %}

{% block title %}Job Status - Video Downloader Service{% endblock %}

{% block content %}
<div class="card">
    <h2>Download Status</h2>
    
    <div class="form-group">
        <label>Job ID</label>
        <input type="text" value="{{ job_id }}" readonly style="background: #f8f9fa;">
    </div>
    
    <div class="form-group">
        <label>URL</label>
        <input type="text" value="{{ job.request.url }}" readonly style="background: #f8f9fa;">
    </div>
    
    <div class="form-group">
        <label>Status</label>
        <div>
            <span class="status status-{{ job.status.value }}" id="job-status">
                {{ job.status.value.title() }}
            </span>
        </div>
    </div>
    
    {% if job.progress %}
    <div class="form-group" id="progress-section">
        <label>Progress</label>
        <div class="progress-bar">
            <div class="progress-fill" id="progress-fill" 
                 style="width: {{ job.progress.progress_percent }}%"></div>
        </div>
        <div style="display: flex; justify-content: space-between; font-size: 14px; color: #7f8c8d;">
            <span id="progress-percent">{{ "%.1f"|format(job.progress.progress_percent) }}%</span>
            <span id="progress-stage">{{ job.progress.current_stage }}</span>
        </div>
        
        {% if job.progress.download_speed %}
        <div style="margin-top: 10px; font-size: 14px; color: #7f8c8d;">
            <span>Speed: <span id="download-speed">{{ "%.2f"|format(job.progress.download_speed / 1024 / 1024) }} MB/s</span></span>
            {% if job.progress.eta %}
            <span style="margin-left: 20px;">ETA: <span id="eta">{{ job.progress.eta }}s</span></span>
            {% endif %}
        </div>
        {% endif %}
    </div>
    {% endif %}
    
    <div class="form-group">
        <label>Created</label>
        <input type="text" value="{{ job.created_at.strftime('%Y-%m-%d %H:%M:%S') }}" 
               readonly style="background: #f8f9fa;">
    </div>
    
    <div class="form-group">
        <label>Last Updated</label>
        <input type="text" value="{{ job.updated_at.strftime('%Y-%m-%d %H:%M:%S') }}" 
               readonly style="background: #f8f9fa;" id="last-updated">
    </div>
    
    {% if job.error_message %}
    <div class="alert alert-error">
        <strong>Error:</strong> {{ job.error_message }}
    </div>
    {% endif %}
    
    {% if job.result %}
    <div class="card" style="background: #d4edda; border: 1px solid #c3e6cb;">
        <h3 style="color: #155724;">Download Completed!</h3>
        
        {% if job.result.metadata %}
        <div class="form-group">
            <label>Title</label>
            <input type="text" value="{{ job.result.metadata.title }}" 
                   readonly style="background: #f8f9fa;">
        </div>
        
        {% if job.result.metadata.duration %}
        <div class="form-group">
            <label>Duration</label>
            <input type="text" value="{{ job.result.metadata.duration // 60 }}:{{ '%02d'|format(job.result.metadata.duration % 60) }}" 
                   readonly style="background: #f8f9fa;">
        </div>
        {% endif %}
        {% endif %}
        
        {% if job.result.file_path %}
        <div class="form-group">
            <label>File Path</label>
            <input type="text" value="{{ job.result.file_path }}" 
                   readonly style="background: #f8f9fa;">
        </div>
        {% endif %}
        
        {% if job.result.file_size %}
        <div class="form-group">
            <label>File Size</label>
            <input type="text" value="{{ '%.2f'|format(job.result.file_size / 1024 / 1024) }} MB" 
                   readonly style="background: #f8f9fa;">
        </div>
        {% endif %}
        
        {% if job.result.processing_time %}
        <div class="form-group">
            <label>Processing Time</label>
            <input type="text" value="{{ '%.2f'|format(job.result.processing_time) }} seconds" 
                   readonly style="background: #f8f9fa;">
        </div>
        {% endif %}
        
        {% if job.result.thumbnail_path %}
        <div class="form-group">
            <label>Thumbnail</label>
            <div>
                <img src="{{ job.result.thumbnail_path }}" alt="Video thumbnail" 
                     style="max-width: 200px; border-radius: 4px;">
            </div>
        </div>
        {% endif %}
    </div>
    {% endif %}
    
    <div style="margin-top: 20px;">
        <a href="/web" class="btn">Back to Home</a>
        <a href="/web/jobs" class="btn">View All Jobs</a>
        
        {% if job.status.value in ['pending', 'downloading', 'processing'] %}
        <button onclick="refreshStatus()" class="btn" id="refresh-btn">Refresh Status</button>
        {% endif %}
    </div>
</div>

<div class="card">
    <h3>Request Details</h3>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
        <div>
            <label>Quality</label>
            <input type="text" value="{{ job.request.quality.value }}" 
                   readonly style="background: #f8f9fa;">
        </div>
        <div>
            <label>Audio Only</label>
            <input type="text" value="{{ 'Yes' if job.request.audio_only else 'No' }}" 
                   readonly style="background: #f8f9fa;">
        </div>
        <div>
            <label>Audio Format</label>
            <input type="text" value="{{ job.request.audio_format.value }}" 
                   readonly style="background: #f8f9fa;">
        </div>
        <div>
            <label>Video Format</label>
            <input type="text" value="{{ job.request.video_format.value }}" 
                   readonly style="background: #f8f9fa;">
        </div>
        <div>
            <label>Extract Thumbnails</label>
            <input type="text" value="{{ 'Yes' if job.request.extract_thumbnails else 'No' }}" 
                   readonly style="background: #f8f9fa;">
        </div>
        <div>
            <label>Extract Subtitles</label>
            <input type="text" value="{{ 'Yes' if job.request.extract_subtitles else 'No' }}" 
                   readonly style="background: #f8f9fa;">
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    let autoRefresh = true;
    
    function refreshStatus() {
        fetch('/web/api/status/{{ job_id }}')
            .then(response => response.json())
            .then(data => {
                updateStatusDisplay(data);
            })
            .catch(error => {
                console.error('Error refreshing status:', error);
            });
    }
    
    function updateStatusDisplay(data) {
        // Update status
        const statusElement = document.getElementById('job-status');
        statusElement.textContent = data.status.charAt(0).toUpperCase() + data.status.slice(1);
        statusElement.className = 'status status-' + data.status;
        
        // Update last updated time
        const lastUpdatedElement = document.getElementById('last-updated');
        if (lastUpdatedElement) {
            const date = new Date(data.updated_at);
            lastUpdatedElement.value = date.toLocaleString();
        }
        
        // Update progress if available
        if (data.progress) {
            const progressSection = document.getElementById('progress-section');
            if (progressSection) {
                progressSection.style.display = 'block';
                
                const progressFill = document.getElementById('progress-fill');
                const progressPercent = document.getElementById('progress-percent');
                const progressStage = document.getElementById('progress-stage');
                
                if (progressFill) progressFill.style.width = data.progress.percent + '%';
                if (progressPercent) progressPercent.textContent = data.progress.percent.toFixed(1) + '%';
                if (progressStage) progressStage.textContent = data.progress.stage;
                
                if (data.progress.speed) {
                    const speedElement = document.getElementById('download-speed');
                    if (speedElement) {
                        speedElement.textContent = (data.progress.speed / 1024 / 1024).toFixed(2) + ' MB/s';
                    }
                }
                
                if (data.progress.eta) {
                    const etaElement = document.getElementById('eta');
                    if (etaElement) {
                        etaElement.textContent = data.progress.eta + 's';
                    }
                }
            }
        }
        
        // Stop auto-refresh if job is completed
        if (['completed', 'failed', 'cancelled'].includes(data.status)) {
            autoRefresh = false;
            const refreshBtn = document.getElementById('refresh-btn');
            if (refreshBtn) {
                refreshBtn.style.display = 'none';
            }
            
            // Reload page to show results
            if (data.status === 'completed') {
                setTimeout(() => {
                    location.reload();
                }, 2000);
            }
        }
    }
    
    // Auto-refresh every 2 seconds for active jobs
    setInterval(function() {
        if (autoRefresh) {
            refreshStatus();
        }
    }, 2000);
    
    // Initial check to see if we should auto-refresh
    document.addEventListener('DOMContentLoaded', function() {
        const status = '{{ job.status.value }}';
        if (['completed', 'failed', 'cancelled'].includes(status)) {
            autoRefresh = false;
            const refreshBtn = document.getElementById('refresh-btn');
            if (refreshBtn) {
                refreshBtn.style.display = 'none';
            }
        }
    });
</script>
{% endblock %}

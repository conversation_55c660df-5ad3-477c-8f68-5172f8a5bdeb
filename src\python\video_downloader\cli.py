#!/usr/bin/env python3
"""
Command-line interface for the video downloader service.
"""

import asyncio
import click
import sys
import json
import time
from pathlib import Path
from typing import List, Optional
import httpx

from .api.models import DownloadRequest, VideoQuality, AudioFormat, VideoFormat
from .service.daemon import VideoDownloaderService
from .utils.config import get_config


@click.group()
@click.option('--config', '-c', help='Configuration file path')
@click.option('--verbose', '-v', is_flag=True, help='Verbose output')
@click.pass_context
def cli(ctx, config, verbose):
    """Video Downloader Service CLI."""
    ctx.ensure_object(dict)
    ctx.obj['config'] = config
    ctx.obj['verbose'] = verbose
    
    if verbose:
        import logging
        logging.basicConfig(level=logging.INFO)


@cli.command()
@click.argument('url')
@click.option('--quality', '-q', type=click.Choice(['360p', '480p', '720p', '1080p', '1440p', '4K', 'best', 'worst']), 
              default='best', help='Video quality')
@click.option('--audio-only', '-a', is_flag=True, help='Download audio only')
@click.option('--audio-format', type=click.Choice(['mp3', 'aac', 'opus', 'm4a']), 
              default='mp3', help='Audio format for audio-only downloads')
@click.option('--video-format', type=click.Choice(['mp4', 'webm', 'mkv']), 
              default='mp4', help='Video format')
@click.option('--output-dir', '-o', help='Output directory')
@click.option('--filename', '-f', help='Custom filename (without extension)')
@click.option('--no-thumbnails', is_flag=True, help='Skip thumbnail extraction')
@click.option('--no-watermark-removal', is_flag=True, help='Skip watermark removal for TikTok')
@click.option('--subtitles', is_flag=True, help='Extract subtitles (YouTube only)')
@click.option('--wait', '-w', is_flag=True, help='Wait for download to complete')
@click.pass_context
def download(ctx, url, quality, audio_only, audio_format, video_format, output_dir, 
             filename, no_thumbnails, no_watermark_removal, subtitles, wait):
    """Download a video from URL."""
    try:
        # Create download request
        request = DownloadRequest(
            url=url,
            quality=VideoQuality(quality),
            audio_only=audio_only,
            audio_format=AudioFormat(audio_format),
            video_format=VideoFormat(video_format),
            extract_subtitles=subtitles,
            extract_thumbnails=not no_thumbnails,
            remove_watermark=not no_watermark_removal,
            custom_filename=filename,
            output_dir=output_dir
        )
        
        # Submit download request
        config = get_config(ctx.obj['config'])
        api_url = f"http://{config.service.host}:{config.service.port}"
        
        response = submit_download_request(api_url, request)
        job_id = response['job_id']
        
        click.echo(f"Download submitted: Job ID {job_id}")
        
        if wait:
            click.echo("Waiting for download to complete...")
            wait_for_completion(api_url, job_id, ctx.obj['verbose'])
        else:
            click.echo(f"Check status with: video-downloader status {job_id}")
            
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('urls_file', type=click.Path(exists=True))
@click.option('--quality', '-q', type=click.Choice(['360p', '480p', '720p', '1080p', '1440p', '4K', 'best', 'worst']), 
              default='best', help='Video quality')
@click.option('--audio-only', '-a', is_flag=True, help='Download audio only')
@click.option('--audio-format', type=click.Choice(['mp3', 'aac', 'opus', 'm4a']), 
              default='mp3', help='Audio format')
@click.option('--video-format', type=click.Choice(['mp4', 'webm', 'mkv']), 
              default='mp4', help='Video format')
@click.option('--output-dir', '-o', help='Output directory')
@click.option('--wait', '-w', is_flag=True, help='Wait for all downloads to complete')
@click.pass_context
def batch(ctx, urls_file, quality, audio_only, audio_format, video_format, output_dir, wait):
    """Download multiple videos from a file containing URLs."""
    try:
        # Read URLs from file
        with open(urls_file, 'r') as f:
            urls = [line.strip() for line in f if line.strip() and not line.startswith('#')]
        
        if not urls:
            click.echo("No URLs found in file", err=True)
            sys.exit(1)
        
        click.echo(f"Found {len(urls)} URLs to download")
        
        # Submit batch request
        config = get_config(ctx.obj['config'])
        api_url = f"http://{config.service.host}:{config.service.port}"
        
        batch_request = {
            "urls": urls,
            "quality": quality,
            "audio_only": audio_only,
            "audio_format": audio_format,
            "video_format": video_format,
            "output_dir": output_dir
        }
        
        response = submit_batch_request(api_url, batch_request)
        job_ids = response['job_ids']
        
        click.echo(f"Batch download submitted: {len(job_ids)} jobs created")
        
        if wait:
            click.echo("Waiting for all downloads to complete...")
            for job_id in job_ids:
                click.echo(f"Waiting for job {job_id}...")
                wait_for_completion(api_url, job_id, ctx.obj['verbose'])
        else:
            click.echo("Job IDs:")
            for job_id in job_ids:
                click.echo(f"  {job_id}")
            
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('job_id')
@click.option('--follow', '-f', is_flag=True, help='Follow progress updates')
@click.pass_context
def status(ctx, job_id, follow):
    """Check status of a download job."""
    try:
        config = get_config(ctx.obj['config'])
        api_url = f"http://{config.service.host}:{config.service.port}"
        
        if follow:
            follow_job_progress(api_url, job_id)
        else:
            status_info = get_job_status(api_url, job_id)
            display_job_status(status_info)
            
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.option('--limit', '-l', default=10, help='Number of recent jobs to show')
@click.pass_context
def list(ctx, limit):
    """List recent download jobs."""
    try:
        config = get_config(ctx.obj['config'])
        api_url = f"http://{config.service.host}:{config.service.port}"
        
        with httpx.Client() as client:
            response = client.get(f"{api_url}/api/jobs/recent?limit={limit}")
            response.raise_for_status()
            data = response.json()
        
        jobs = data['recent_jobs']
        
        if not jobs:
            click.echo("No recent jobs found")
            return
        
        click.echo(f"Recent {len(jobs)} jobs:")
        click.echo()
        
        for job in jobs:
            status_color = 'green' if job['status'] == 'completed' else 'red' if job['status'] == 'failed' else 'yellow'
            click.echo(f"Job ID: {job['job_id']}")
            click.echo(f"Status: {click.style(job['status'], fg=status_color)}")
            click.echo(f"URL: {job['url']}")
            click.echo(f"Created: {job['created_at']}")
            
            if job.get('result'):
                result = job['result']
                if result.get('file_path'):
                    click.echo(f"File: {result['file_path']}")
                if result.get('processing_time'):
                    click.echo(f"Processing time: {result['processing_time']:.2f}s")
            
            if job.get('error_message'):
                click.echo(f"Error: {click.style(job['error_message'], fg='red')}")
            
            click.echo()
            
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.argument('job_id')
@click.pass_context
def cancel(ctx, job_id):
    """Cancel a download job."""
    try:
        config = get_config(ctx.obj['config'])
        api_url = f"http://{config.service.host}:{config.service.port}"
        
        with httpx.Client() as client:
            response = client.delete(f"{api_url}/api/download/{job_id}")
            response.raise_for_status()
            data = response.json()
        
        click.echo(data['message'])
        
    except httpx.HTTPStatusError as e:
        if e.response.status_code == 404:
            click.echo(f"Job {job_id} not found", err=True)
        else:
            click.echo(f"Error: {e.response.text}", err=True)
        sys.exit(1)
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.option('--daemon', '-d', is_flag=True, help='Run as daemon')
@click.option('--console', is_flag=True, help='Run with console output')
@click.pass_context
def serve(ctx, daemon, console):
    """Start the video downloader service."""
    try:
        service = VideoDownloaderService(config_file=ctx.obj['config'])
        asyncio.run(service.start(daemon_mode=daemon and not console))
    except KeyboardInterrupt:
        click.echo("Service stopped")
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


@cli.command()
@click.pass_context
def health(ctx):
    """Check service health."""
    try:
        config = get_config(ctx.obj['config'])
        api_url = f"http://{config.service.host}:{config.service.port}"
        
        with httpx.Client() as client:
            response = client.get(f"{api_url}/health")
            response.raise_for_status()
            data = response.json()
        
        status_color = 'green' if data['status'] == 'healthy' else 'red'
        click.echo(f"Status: {click.style(data['status'], fg=status_color)}")
        click.echo(f"Version: {data['version']}")
        click.echo(f"Uptime: {data.get('uptime', 0):.2f}s")
        
        if 'active_downloads' in data:
            click.echo(f"Active downloads: {data['active_downloads']}")
        if 'queue_size' in data:
            click.echo(f"Queue size: {data['queue_size']}")
            
    except httpx.ConnectError:
        click.echo(click.style("Service not running", fg='red'), err=True)
        sys.exit(1)
    except Exception as e:
        click.echo(f"Error: {e}", err=True)
        sys.exit(1)


# Helper functions
def submit_download_request(api_url: str, request: DownloadRequest) -> dict:
    """Submit download request to API."""
    with httpx.Client() as client:
        response = client.post(
            f"{api_url}/api/download",
            json=request.model_dump(),
            headers={"Content-Type": "application/json"}
        )
        response.raise_for_status()
        return response.json()


def submit_batch_request(api_url: str, request: dict) -> dict:
    """Submit batch download request to API."""
    with httpx.Client() as client:
        response = client.post(
            f"{api_url}/api/download/batch",
            json=request,
            headers={"Content-Type": "application/json"}
        )
        response.raise_for_status()
        return response.json()


def get_job_status(api_url: str, job_id: str) -> dict:
    """Get job status from API."""
    with httpx.Client() as client:
        response = client.get(f"{api_url}/api/status/{job_id}")
        response.raise_for_status()
        return response.json()


def wait_for_completion(api_url: str, job_id: str, verbose: bool = False):
    """Wait for job completion with progress updates."""
    while True:
        try:
            status_info = get_job_status(api_url, job_id)
            status = status_info['status']
            
            if verbose:
                display_job_status(status_info)
            
            if status in ['completed', 'failed', 'cancelled']:
                break
            
            time.sleep(2)
            
        except KeyboardInterrupt:
            click.echo("\nCancelling wait...")
            break
        except Exception as e:
            click.echo(f"Error checking status: {e}", err=True)
            break
    
    # Final status
    try:
        final_status = get_job_status(api_url, job_id)
        display_job_status(final_status)
    except:
        pass


def follow_job_progress(api_url: str, job_id: str):
    """Follow job progress with live updates."""
    click.echo(f"Following progress for job {job_id} (Press Ctrl+C to stop)")
    
    try:
        while True:
            status_info = get_job_status(api_url, job_id)
            
            # Clear screen and show status
            click.clear()
            display_job_status(status_info)
            
            if status_info['status'] in ['completed', 'failed', 'cancelled']:
                break
            
            time.sleep(1)
            
    except KeyboardInterrupt:
        click.echo("\nStopped following progress")


def display_job_status(status_info: dict):
    """Display job status information."""
    status = status_info['status']
    status_color = 'green' if status == 'completed' else 'red' if status == 'failed' else 'yellow'
    
    click.echo(f"Job ID: {status_info['job_id']}")
    click.echo(f"Status: {click.style(status, fg=status_color)}")
    
    if 'progress' in status_info and status_info['progress']:
        progress = status_info['progress']
        percent = progress.get('progress_percent', 0)
        stage = progress.get('current_stage', 'unknown')
        
        click.echo(f"Progress: {percent:.1f}% ({stage})")
        
        if progress.get('download_speed'):
            speed_mb = progress['download_speed'] / 1024 / 1024
            click.echo(f"Speed: {speed_mb:.2f} MB/s")
        
        if progress.get('eta'):
            click.echo(f"ETA: {progress['eta']}s")
    
    if 'result' in status_info and status_info['result']:
        result = status_info['result']
        if result.get('file_path'):
            click.echo(f"File: {result['file_path']}")
        if result.get('processing_time'):
            click.echo(f"Processing time: {result['processing_time']:.2f}s")


def main():
    """Main CLI entry point."""
    cli()


if __name__ == '__main__':
    main()

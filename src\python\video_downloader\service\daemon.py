"""
Main daemon service for the video downloader.
"""

import asyncio
import signal
import sys
import os
import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
import uvicorn
from fastapi import FastAPI

from ..utils.config import get_config
from ..queue.job_queue import get_job_queue_manager, JobStatus
from ..api.app import create_app
from ..downloaders.worker import DownloadWorker


logger = logging.getLogger(__name__)


class VideoDownloaderService:
    """Main service class that orchestrates the video downloader daemon."""
    
    def __init__(self, config_file: Optional[str] = None):
        """Initialize the video downloader service."""
        self.config = get_config(config_file)
        self.app: Optional[FastAPI] = None
        self.workers: list = []
        self.running = False
        self.start_time = datetime.now()
        
        # Setup logging
        self._setup_logging()
        
        # Setup signal handlers
        self._setup_signal_handlers()
        
        # Initialize job queue manager
        self.queue_manager = get_job_queue_manager()
        
        logger.info(f"Video Downloader Service v{self.config.service.version} initialized")
    
    def _setup_logging(self):
        """Setup logging configuration."""
        log_level = getattr(logging, self.config.logging.level.upper())
        log_format = self.config.logging.format
        
        # Create logs directory
        Path(self.config.storage.logs_dir).mkdir(parents=True, exist_ok=True)
        
        # Configure root logger
        logging.basicConfig(
            level=log_level,
            format=log_format,
            handlers=[]
        )
        
        # Console handler
        if self.config.logging.console_logging:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(log_level)
            console_formatter = logging.Formatter(log_format)
            console_handler.setFormatter(console_formatter)
            logging.getLogger().addHandler(console_handler)
        
        # File handler
        if self.config.logging.file_logging:
            log_file = Path(self.config.storage.logs_dir) / "service.log"
            file_handler = logging.FileHandler(log_file)
            file_handler.setLevel(log_level)
            file_formatter = logging.Formatter(log_format)
            file_handler.setFormatter(file_formatter)
            logging.getLogger().addHandler(file_handler)
    
    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating graceful shutdown...")
            asyncio.create_task(self.stop())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        if hasattr(signal, 'SIGHUP'):
            signal.signal(signal.SIGHUP, signal_handler)
    
    async def start(self, daemon_mode: bool = None):
        """Start the video downloader service."""
        if self.running:
            logger.warning("Service is already running")
            return
        
        daemon_mode = daemon_mode if daemon_mode is not None else self.config.service.daemon_mode
        
        logger.info("Starting Video Downloader Service...")
        
        try:
            # Create FastAPI app
            self.app = create_app()
            
            # Start download workers
            await self._start_workers()
            
            # Start cleanup task
            asyncio.create_task(self._cleanup_task())
            
            self.running = True
            logger.info("Video Downloader Service started successfully")
            
            if daemon_mode:
                await self._run_daemon()
            else:
                await self._run_server()
                
        except Exception as e:
            logger.error(f"Failed to start service: {e}")
            await self.stop()
            raise
    
    async def _start_workers(self):
        """Start download worker processes."""
        max_workers = self.config.service.max_concurrent_downloads
        
        for i in range(max_workers):
            worker = DownloadWorker(
                worker_id=f"worker-{i}",
                queue_manager=self.queue_manager,
                config=self.config
            )
            self.workers.append(worker)
            asyncio.create_task(worker.start())
        
        logger.info(f"Started {len(self.workers)} download workers")
    
    async def _run_server(self):
        """Run the HTTP server."""
        config = uvicorn.Config(
            app=self.app,
            host=self.config.service.host,
            port=self.config.service.port,
            workers=1,  # We handle workers ourselves
            log_level=self.config.logging.level.lower(),
            access_log=True,
        )
        
        server = uvicorn.Server(config)
        await server.serve()
    
    async def _run_daemon(self):
        """Run as a daemon process."""
        # Write PID file
        if self.config.service.pid_file:
            pid_file = Path(self.config.service.pid_file)
            pid_file.parent.mkdir(parents=True, exist_ok=True)
            pid_file.write_text(str(os.getpid()))
        
        # Start HTTP server in background
        server_task = asyncio.create_task(self._run_server())
        
        try:
            # Keep the daemon running
            while self.running:
                await asyncio.sleep(1)
        except asyncio.CancelledError:
            pass
        finally:
            server_task.cancel()
            try:
                await server_task
            except asyncio.CancelledError:
                pass
    
    async def _cleanup_task(self):
        """Background task for periodic cleanup."""
        while self.running:
            try:
                # Cleanup old jobs
                if hasattr(self.queue_manager.queue, 'cleanup_old_jobs'):
                    cleaned = await self.queue_manager.queue.cleanup_old_jobs(
                        max_age_days=self.config.monitoring.stats_retention_days
                    )
                    if cleaned > 0:
                        logger.info(f"Cleaned up {cleaned} old jobs")
                
                # Cleanup temp files
                if self.config.storage.cleanup_temp_files:
                    await self._cleanup_temp_files()
                
                # Wait for next cleanup cycle
                await asyncio.sleep(self.config.storage.cleanup_interval)
                
            except Exception as e:
                logger.error(f"Error in cleanup task: {e}")
                await asyncio.sleep(60)  # Wait a minute before retrying
    
    async def _cleanup_temp_files(self):
        """Clean up temporary files."""
        try:
            temp_dir = Path(self.config.storage.temp_dir)
            if not temp_dir.exists():
                return
            
            # Remove files older than 1 hour
            cutoff_time = time.time() - 3600
            cleaned_count = 0
            
            for file_path in temp_dir.rglob("*"):
                if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                    try:
                        file_path.unlink()
                        cleaned_count += 1
                    except Exception as e:
                        logger.warning(f"Failed to delete temp file {file_path}: {e}")
            
            if cleaned_count > 0:
                logger.info(f"Cleaned up {cleaned_count} temporary files")
                
        except Exception as e:
            logger.error(f"Error cleaning up temp files: {e}")
    
    async def stop(self):
        """Stop the video downloader service."""
        if not self.running:
            return
        
        logger.info("Stopping Video Downloader Service...")
        self.running = False
        
        try:
            # Stop all workers
            for worker in self.workers:
                await worker.stop()
            
            # Clean up PID file
            if self.config.service.pid_file:
                pid_file = Path(self.config.service.pid_file)
                if pid_file.exists():
                    pid_file.unlink()
            
            logger.info("Video Downloader Service stopped successfully")
            
        except Exception as e:
            logger.error(f"Error during service shutdown: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get service status information."""
        uptime = (datetime.now() - self.start_time).total_seconds()
        
        return {
            "status": "running" if self.running else "stopped",
            "version": self.config.service.version,
            "uptime": uptime,
            "start_time": self.start_time.isoformat(),
            "workers": len(self.workers),
            "config": {
                "host": self.config.service.host,
                "port": self.config.service.port,
                "max_concurrent_downloads": self.config.service.max_concurrent_downloads,
            }
        }


async def main():
    """Main entry point for the service."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Video Downloader Service")
    parser.add_argument("--config", "-c", help="Configuration file path")
    parser.add_argument("--daemon", "-d", action="store_true", help="Run as daemon")
    parser.add_argument("--console", action="store_true", help="Run with console output")
    parser.add_argument("--stop", action="store_true", help="Stop running daemon")
    parser.add_argument("--status", action="store_true", help="Show service status")
    
    args = parser.parse_args()
    
    if args.stop:
        # TODO: Implement daemon stop functionality
        print("Stop functionality not yet implemented")
        return
    
    if args.status:
        # TODO: Implement status check functionality
        print("Status functionality not yet implemented")
        return
    
    # Create and start service
    service = VideoDownloaderService(config_file=args.config)
    
    try:
        await service.start(daemon_mode=args.daemon and not args.console)
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Service error: {e}")
        sys.exit(1)
    finally:
        await service.stop()


if __name__ == "__main__":
    asyncio.run(main())

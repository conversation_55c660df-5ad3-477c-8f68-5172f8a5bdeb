"""
Pydantic models for the video downloader API.
"""

from datetime import datetime
from enum import Enum
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, HttpUrl, validator


class Platform(str, Enum):
    """Supported platforms."""
    YOUTUBE = "youtube"
    TIKTOK = "tiktok"
    FACEBOOK = "facebook"


class VideoQuality(str, Enum):
    """Video quality options."""
    AUDIO_ONLY = "audio_only"
    Q360P = "360p"
    Q480P = "480p"
    Q720P = "720p"
    Q1080P = "1080p"
    Q1440P = "1440p"
    Q2160P = "4K"
    BEST = "best"
    WORST = "worst"


class AudioFormat(str, Enum):
    """Audio format options."""
    MP3 = "mp3"
    AAC = "aac"
    OPUS = "opus"
    M4A = "m4a"
    WAV = "wav"


class VideoFormat(str, Enum):
    """Video format options."""
    MP4 = "mp4"
    WEBM = "webm"
    MKV = "mkv"
    AVI = "avi"
    MOV = "mov"


class JobStatus(str, Enum):
    """Job status options."""
    PENDING = "pending"
    DOWNLOADING = "downloading"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class DownloadRequest(BaseModel):
    """Request model for video download."""
    url: HttpUrl = Field(..., description="Video URL to download")
    quality: VideoQuality = Field(VideoQuality.BEST, description="Video quality preference")
    audio_only: bool = Field(False, description="Download audio only")
    audio_format: AudioFormat = Field(AudioFormat.MP3, description="Audio format for audio-only downloads")
    video_format: VideoFormat = Field(VideoFormat.MP4, description="Video format preference")
    extract_subtitles: bool = Field(False, description="Extract subtitles if available")
    extract_thumbnails: bool = Field(True, description="Extract video thumbnails")
    remove_watermark: bool = Field(True, description="Remove watermarks (TikTok only)")
    custom_filename: Optional[str] = Field(None, description="Custom filename (without extension)")
    output_dir: Optional[str] = Field(None, description="Custom output directory")
    
    @validator('url')
    def validate_url(cls, v):
        """Validate that the URL is from a supported platform."""
        url_str = str(v)
        supported_domains = [
            'youtube.com', 'youtu.be', 'm.youtube.com',
            'tiktok.com', 'vm.tiktok.com', 'm.tiktok.com',
            'facebook.com', 'fb.watch', 'm.facebook.com'
        ]
        
        if not any(domain in url_str for domain in supported_domains):
            raise ValueError(f"URL must be from a supported platform: {', '.join(supported_domains)}")
        
        return v
    
    @validator('custom_filename')
    def validate_filename(cls, v):
        """Validate custom filename."""
        if v is not None:
            # Remove invalid characters
            invalid_chars = '<>:"/\\|?*'
            if any(char in v for char in invalid_chars):
                raise ValueError(f"Filename cannot contain: {invalid_chars}")
        return v


class VideoMetadata(BaseModel):
    """Video metadata information."""
    title: str
    description: Optional[str] = None
    duration: Optional[int] = None  # in seconds
    uploader: Optional[str] = None
    upload_date: Optional[datetime] = None
    view_count: Optional[int] = None
    like_count: Optional[int] = None
    thumbnail_url: Optional[str] = None
    platform: Platform
    original_url: str
    video_id: str
    tags: List[str] = []
    categories: List[str] = []


class DownloadProgress(BaseModel):
    """Download progress information."""
    job_id: str
    status: JobStatus
    progress_percent: float = Field(0.0, ge=0.0, le=100.0)
    downloaded_bytes: int = 0
    total_bytes: Optional[int] = None
    download_speed: Optional[float] = None  # bytes per second
    eta: Optional[int] = None  # estimated time remaining in seconds
    current_stage: str = "initializing"
    error_message: Optional[str] = None
    created_at: datetime
    updated_at: datetime


class DownloadResult(BaseModel):
    """Download result information."""
    job_id: str
    status: JobStatus
    metadata: Optional[VideoMetadata] = None
    file_path: Optional[str] = None
    file_size: Optional[int] = None
    thumbnail_path: Optional[str] = None
    subtitle_paths: List[str] = []
    processing_time: Optional[float] = None  # in seconds
    error_message: Optional[str] = None
    created_at: datetime
    completed_at: Optional[datetime] = None


class DownloadResponse(BaseModel):
    """Response model for download request."""
    job_id: str = Field(..., description="Unique job identifier")
    status: JobStatus = Field(..., description="Current job status")
    message: str = Field(..., description="Response message")
    estimated_time: Optional[int] = Field(None, description="Estimated completion time in seconds")
    queue_position: Optional[int] = Field(None, description="Position in download queue")


class JobStatusResponse(BaseModel):
    """Response model for job status check."""
    job_id: str
    status: JobStatus
    progress: DownloadProgress
    result: Optional[DownloadResult] = None


class DownloadListResponse(BaseModel):
    """Response model for listing downloads."""
    downloads: List[DownloadResult]
    total_count: int
    page: int = 1
    page_size: int = 50
    has_next: bool = False
    has_previous: bool = False


class HealthResponse(BaseModel):
    """Health check response."""
    status: str = "healthy"
    timestamp: datetime
    version: str
    uptime: float  # in seconds
    active_downloads: int
    queue_size: int
    system_info: Dict[str, Any] = {}


class MetricsResponse(BaseModel):
    """Metrics response."""
    total_downloads: int
    successful_downloads: int
    failed_downloads: int
    average_download_time: float
    platform_stats: Dict[Platform, Dict[str, int]]
    quality_stats: Dict[VideoQuality, int]
    format_stats: Dict[str, int]
    timestamp: datetime


class ErrorResponse(BaseModel):
    """Error response model."""
    error: str
    message: str
    details: Optional[Dict[str, Any]] = None
    timestamp: datetime
    request_id: Optional[str] = None


class BatchDownloadRequest(BaseModel):
    """Batch download request model."""
    urls: List[HttpUrl] = Field(..., min_items=1, max_items=100)
    quality: VideoQuality = Field(VideoQuality.BEST)
    audio_only: bool = Field(False)
    audio_format: AudioFormat = Field(AudioFormat.MP3)
    video_format: VideoFormat = Field(VideoFormat.MP4)
    extract_subtitles: bool = Field(False)
    extract_thumbnails: bool = Field(True)
    remove_watermark: bool = Field(True)
    output_dir: Optional[str] = Field(None)


class BatchDownloadResponse(BaseModel):
    """Batch download response model."""
    batch_id: str
    job_ids: List[str]
    total_jobs: int
    message: str
    estimated_total_time: Optional[int] = None


# Configuration models for API responses
class PlatformConfigResponse(BaseModel):
    """Platform configuration response."""
    platform: Platform
    enabled: bool
    max_quality: VideoQuality
    supported_audio_formats: List[AudioFormat]
    supported_video_formats: List[VideoFormat]
    features: Dict[str, bool]


class ServiceConfigResponse(BaseModel):
    """Service configuration response."""
    version: str
    max_concurrent_downloads: int
    supported_platforms: List[PlatformConfigResponse]
    max_file_size: str
    rate_limits: Dict[str, str]

"""
Video Downloader Service

A high-performance Python/C++ hybrid application for downloading videos
from multiple platforms including YouTube, TikTok, and Facebook.
"""

__version__ = "1.0.0"
__author__ = "Video Downloader Team"
__email__ = "<EMAIL>"
__license__ = "MIT"

from .service.daemon import VideoDownloaderService
from .api.models import DownloadRequest, DownloadResponse, JobStatus
from .downloaders.factory import DownloaderFactory
from .utils.config import Config

# Public API
__all__ = [
    "VideoDownloaderService",
    "DownloadRequest", 
    "DownloadResponse",
    "JobStatus",
    "DownloaderFactory",
    "Config",
]

# Version info
VERSION_INFO = tuple(map(int, __version__.split(".")))

# Package metadata
PACKAGE_INFO = {
    "name": "video-downloader-service",
    "version": __version__,
    "description": "Multi-platform video downloader service",
    "author": __author__,
    "email": __email__,
    "license": __license__,
    "url": "https://github.com/your-org/video-downloader-service",
}

def get_version():
    """Get the package version."""
    return __version__

def get_package_info():
    """Get package information."""
    return PACKAGE_INFO.copy()

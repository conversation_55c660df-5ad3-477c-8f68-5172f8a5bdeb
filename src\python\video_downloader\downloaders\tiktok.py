"""
TikTok downloader implementation with watermark removal support.
"""

import asyncio
import logging
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List
import yt_dlp

from .base import BaseDownloader, ProgressCallback, DownloadError, VideoNotFoundError
from ..api.models import (
    DownloadRequest, VideoMetadata, Platform, VideoQuality, 
    AudioFormat, VideoFormat
)
from ..utils.config import Config


logger = logging.getLogger(__name__)


class TikTokDownloader(BaseDownloader):
    """TikTok downloader with watermark removal support."""
    
    def __init__(self, config: Config):
        """Initialize TikTok downloader."""
        super().__init__(config)
        self.platform = Platform.TIKTOK
        self.supported_domains = [
            'tiktok.com', 'vm.tiktok.com', 'm.tiktok.com',
            'www.tiktok.com', 'vt.tiktok.com'
        ]
    
    def can_handle(self, url: str) -> bool:
        """Check if this downloader can handle the given URL."""
        url_info = self._parse_url(url)
        return any(domain in url_info['domain'] for domain in self.supported_domains)
    
    async def extract_metadata(self, url: str) -> VideoMetadata:
        """Extract video metadata without downloading."""
        try:
            # Resolve short URLs first
            resolved_url = await self._resolve_url(url)
            
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
                'extract_flat': False,
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                loop = asyncio.get_event_loop()
                info = await loop.run_in_executor(
                    None, lambda: ydl.extract_info(resolved_url, download=False)
                )
            
            if not info:
                raise VideoNotFoundError(f"Could not extract info from TikTok URL: {url}")
            
            # Parse upload date
            upload_date = None
            if info.get('timestamp'):
                upload_date = datetime.fromtimestamp(info['timestamp'])
            elif info.get('upload_date'):
                try:
                    upload_date = datetime.strptime(info['upload_date'], '%Y%m%d')
                except ValueError:
                    pass
            
            # Extract video ID from URL or info
            video_id = self._extract_video_id(resolved_url) or info.get('id', '')
            
            # Create metadata object
            metadata = VideoMetadata(
                title=info.get('title', 'TikTok Video'),
                description=info.get('description', ''),
                duration=info.get('duration'),
                uploader=info.get('uploader', ''),
                upload_date=upload_date,
                view_count=info.get('view_count'),
                like_count=info.get('like_count'),
                thumbnail_url=info.get('thumbnail', ''),
                platform=self.platform,
                original_url=url,
                video_id=video_id,
                tags=self._extract_hashtags(info.get('description', '')),
                categories=[]
            )
            
            return metadata
            
        except Exception as e:
            logger.error(f"Failed to extract TikTok metadata: {e}")
            raise DownloadError(f"Failed to extract metadata: {str(e)}")
    
    async def get_available_qualities(self, url: str) -> List[VideoQuality]:
        """Get available video qualities for the URL."""
        # TikTok typically has limited quality options
        return [
            VideoQuality.BEST,
            VideoQuality.Q1080P,
            VideoQuality.Q720P,
            VideoQuality.Q480P,
            VideoQuality.AUDIO_ONLY
        ]
    
    async def get_available_formats(self, url: str, audio_only: bool = False) -> List[str]:
        """Get available formats for the URL."""
        if audio_only:
            return ['mp3', 'aac']
        else:
            return ['mp4']
    
    async def download(
        self, 
        request: DownloadRequest, 
        progress_callback: Optional[ProgressCallback] = None
    ) -> Dict[str, Any]:
        """Download TikTok video with optional watermark removal."""
        self._validate_request(request)
        
        try:
            # Resolve URL first
            resolved_url = await self._resolve_url(str(request.url))
            
            # Extract metadata
            metadata = await self.extract_metadata(str(request.url))
            
            # Get output path
            output_path = self._get_output_path(request, metadata)
            temp_path = self._get_temp_path(output_path)
            
            # Configure yt-dlp options
            ydl_opts = self._get_ydl_options(request, temp_path, progress_callback)
            
            # Download the video
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(
                    None, lambda: ydl.download([resolved_url])
                )
            
            # Find the downloaded file
            downloaded_file = self._find_downloaded_file(temp_path)
            if not downloaded_file:
                raise DownloadError("Downloaded file not found")
            
            # Remove watermark if requested and not audio-only
            if request.remove_watermark and not request.audio_only:
                processed_file = await self._remove_watermark(downloaded_file, temp_path.parent)
                if processed_file and processed_file.exists():
                    downloaded_file.unlink()  # Remove original
                    downloaded_file = processed_file
            
            # Move to final location
            downloaded_file.rename(output_path)
            
            # Post-process
            result = await self._post_process(output_path, request, metadata)
            
            logger.info(f"Successfully downloaded TikTok video: {metadata.title}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to download TikTok video: {e}")
            # Cleanup temp files
            if 'temp_path' in locals():
                self._cleanup_temp_files(temp_path.parent)
            raise DownloadError(f"Download failed: {str(e)}")
    
    async def _resolve_url(self, url: str) -> str:
        """Resolve TikTok short URLs to full URLs."""
        import aiohttp
        
        # If it's already a full URL, return as-is
        if 'tiktok.com/@' in url or '/video/' in url:
            return url
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.head(url, allow_redirects=True) as response:
                    return str(response.url)
        except Exception as e:
            logger.warning(f"Failed to resolve TikTok URL {url}: {e}")
            return url
    
    def _extract_video_id(self, url: str) -> Optional[str]:
        """Extract video ID from TikTok URL."""
        patterns = [
            r'/video/(\d+)',
            r'@[\w.-]+/video/(\d+)',
            r'v/(\d+)',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)
        
        return None
    
    def _extract_hashtags(self, description: str) -> List[str]:
        """Extract hashtags from description."""
        if not description:
            return []
        
        hashtag_pattern = r'#(\w+)'
        hashtags = re.findall(hashtag_pattern, description)
        return hashtags
    
    def _get_ydl_options(
        self, 
        request: DownloadRequest, 
        output_path: Path,
        progress_callback: Optional[ProgressCallback] = None
    ) -> Dict[str, Any]:
        """Get yt-dlp options for TikTok download."""
        opts = {
            'outtmpl': str(output_path.with_suffix('')),
            'quiet': True,
            'no_warnings': True,
        }
        
        # Quality and format selection
        if request.audio_only:
            opts['format'] = 'bestaudio/best'
            opts['postprocessors'] = [{
                'key': 'FFmpegExtractAudio',
                'preferredcodec': request.audio_format.value,
                'preferredquality': '192',
            }]
        else:
            # TikTok video format
            if request.quality == VideoQuality.BEST:
                opts['format'] = 'best[ext=mp4]/best'
            elif request.quality == VideoQuality.Q1080P:
                opts['format'] = 'best[height<=1080][ext=mp4]/best[height<=1080]/best'
            elif request.quality == VideoQuality.Q720P:
                opts['format'] = 'best[height<=720][ext=mp4]/best[height<=720]/best'
            elif request.quality == VideoQuality.Q480P:
                opts['format'] = 'best[height<=480][ext=mp4]/best[height<=480]/best'
            else:
                opts['format'] = 'best[ext=mp4]/best'
        
        # Thumbnail extraction
        if request.extract_thumbnails:
            opts['writethumbnail'] = True
        
        # Progress hook
        if progress_callback:
            opts['progress_hooks'] = [self._create_progress_hook(progress_callback)]
        
        return opts
    
    def _create_progress_hook(self, progress_callback: ProgressCallback):
        """Create progress hook for yt-dlp."""
        def hook(d):
            try:
                if d['status'] == 'downloading':
                    total_bytes = d.get('total_bytes') or d.get('total_bytes_estimate')
                    downloaded_bytes = d.get('downloaded_bytes', 0)
                    speed = d.get('speed')
                    eta = d.get('eta')
                    
                    progress_percent = 0.0
                    if total_bytes and total_bytes > 0:
                        progress_percent = (downloaded_bytes / total_bytes) * 100
                    
                    progress_data = {
                        'progress_percent': min(progress_percent, 100.0),
                        'downloaded_bytes': downloaded_bytes,
                        'total_bytes': total_bytes,
                        'download_speed': speed,
                        'eta': eta,
                        'current_stage': 'downloading'
                    }
                    
                    progress_callback(progress_data)
                    
                elif d['status'] == 'finished':
                    progress_data = {
                        'progress_percent': 100.0,
                        'downloaded_bytes': d.get('total_bytes', 0),
                        'total_bytes': d.get('total_bytes', 0),
                        'current_stage': 'finished'
                    }
                    progress_callback(progress_data)
                    
            except Exception as e:
                logger.error(f"Error in TikTok progress hook: {e}")
        
        return hook
    
    def _find_downloaded_file(self, temp_path: Path) -> Optional[Path]:
        """Find the downloaded file in temp directory."""
        # Look for files with similar name
        pattern = f"{temp_path.stem}*"
        files = list(temp_path.parent.glob(pattern))
        
        # Filter out non-video files
        video_extensions = ['.mp4', '.webm', '.mkv', '.avi']
        video_files = [f for f in files if f.suffix.lower() in video_extensions]
        
        if video_files:
            return video_files[0]
        
        # If no video files, return any file (might be audio)
        if files:
            return files[0]
        
        return None
    
    async def _remove_watermark(self, input_file: Path, temp_dir: Path) -> Optional[Path]:
        """Remove watermark from TikTok video using FFmpeg."""
        try:
            # Check if C++ video processor is available
            if self.config.processing.use_cpp_engine:
                return await self._remove_watermark_cpp(input_file, temp_dir)
            else:
                return await self._remove_watermark_ffmpeg(input_file, temp_dir)
        except Exception as e:
            logger.warning(f"Failed to remove watermark: {e}")
            return input_file  # Return original file if watermark removal fails
    
    async def _remove_watermark_ffmpeg(self, input_file: Path, temp_dir: Path) -> Optional[Path]:
        """Remove watermark using FFmpeg."""
        import subprocess
        
        output_file = temp_dir / f"{input_file.stem}_no_watermark{input_file.suffix}"
        
        # FFmpeg command to crop out watermark area (bottom-right corner)
        cmd = [
            self.config.processing.ffmpeg_path,
            '-i', str(input_file),
            '-vf', 'crop=iw-100:ih-100:0:0',  # Crop 100px from right and bottom
            '-c:a', 'copy',  # Copy audio without re-encoding
            '-y',  # Overwrite output file
            str(output_file)
        ]
        
        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0 and output_file.exists():
                return output_file
            else:
                logger.error(f"FFmpeg watermark removal failed: {stderr.decode()}")
                return input_file
                
        except Exception as e:
            logger.error(f"Error running FFmpeg for watermark removal: {e}")
            return input_file
    
    async def _remove_watermark_cpp(self, input_file: Path, temp_dir: Path) -> Optional[Path]:
        """Remove watermark using C++ video processor."""
        try:
            # Import C++ video processor
            import video_processor_cpp
            
            output_file = temp_dir / f"{input_file.stem}_no_watermark{input_file.suffix}"
            
            # Use C++ processor for watermark removal
            success = video_processor_cpp.remove_watermark(
                str(input_file),
                str(output_file),
                crop_right=100,
                crop_bottom=100
            )
            
            if success and output_file.exists():
                return output_file
            else:
                logger.warning("C++ watermark removal failed, falling back to original")
                return input_file
                
        except ImportError:
            logger.warning("C++ video processor not available, using FFmpeg")
            return await self._remove_watermark_ffmpeg(input_file, temp_dir)
        except Exception as e:
            logger.error(f"Error in C++ watermark removal: {e}")
            return input_file
    
    def _cleanup_temp_files(self, temp_dir: Path):
        """Clean up temporary files."""
        try:
            for file in temp_dir.glob("*"):
                if file.is_file():
                    file.unlink()
        except Exception as e:
            logger.warning(f"Failed to cleanup temp files: {e}")

"""
Base downloader class and interfaces.
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, Callable, List
from urllib.parse import urlparse

from ..api.models import (
    DownloadRequest, VideoMetadata, DownloadProgress, 
    Platform, JobStatus, VideoQuality, AudioFormat, VideoFormat
)
from ..utils.config import Config


logger = logging.getLogger(__name__)


class DownloadError(Exception):
    """Base exception for download errors."""
    pass


class UnsupportedPlatformError(DownloadError):
    """Raised when a platform is not supported."""
    pass


class VideoNotFoundError(DownloadError):
    """Raised when a video is not found or unavailable."""
    pass


class QualityNotAvailableError(DownloadError):
    """Raised when requested quality is not available."""
    pass


class ProgressCallback:
    """Progress callback handler."""
    
    def __init__(self, job_id: str, update_callback: Optional[Callable] = None):
        """Initialize progress callback."""
        self.job_id = job_id
        self.update_callback = update_callback
        self.last_update = datetime.now()
    
    def __call__(self, progress_data: Dict[str, Any]):
        """Handle progress update."""
        try:
            # Create progress object
            progress = DownloadProgress(
                job_id=self.job_id,
                status=JobStatus.DOWNLOADING,
                progress_percent=progress_data.get('progress_percent', 0.0),
                downloaded_bytes=progress_data.get('downloaded_bytes', 0),
                total_bytes=progress_data.get('total_bytes'),
                download_speed=progress_data.get('download_speed'),
                eta=progress_data.get('eta'),
                current_stage=progress_data.get('current_stage', 'downloading'),
                created_at=self.last_update,
                updated_at=datetime.now()
            )
            
            # Call update callback if provided
            if self.update_callback:
                asyncio.create_task(self.update_callback(progress))
            
            self.last_update = datetime.now()
            
        except Exception as e:
            logger.error(f"Error in progress callback: {e}")


class BaseDownloader(ABC):
    """Base class for platform-specific downloaders."""
    
    def __init__(self, config: Config):
        """Initialize base downloader."""
        self.config = config
        self.platform: Platform = None
        self.supported_domains: List[str] = []
    
    @abstractmethod
    def can_handle(self, url: str) -> bool:
        """Check if this downloader can handle the given URL."""
        pass
    
    @abstractmethod
    async def extract_metadata(self, url: str) -> VideoMetadata:
        """Extract video metadata without downloading."""
        pass
    
    @abstractmethod
    async def get_available_qualities(self, url: str) -> List[VideoQuality]:
        """Get available video qualities for the URL."""
        pass
    
    @abstractmethod
    async def get_available_formats(self, url: str, audio_only: bool = False) -> List[str]:
        """Get available formats for the URL."""
        pass
    
    @abstractmethod
    async def download(
        self, 
        request: DownloadRequest, 
        progress_callback: Optional[ProgressCallback] = None
    ) -> Dict[str, Any]:
        """Download video according to the request."""
        pass
    
    def _get_output_path(self, request: DownloadRequest, metadata: VideoMetadata) -> Path:
        """Generate output path for the download."""
        # Use custom output directory if specified
        if request.output_dir:
            base_dir = Path(request.output_dir)
        else:
            base_dir = Path(self.config.storage.download_dir)
        
        # Organize by platform if enabled
        if self.config.storage.organize_by_platform:
            base_dir = base_dir / self.platform.value
        
        # Organize by date if enabled
        if self.config.storage.organize_by_date:
            today = datetime.now().strftime("%Y-%m-%d")
            base_dir = base_dir / today
        
        # Create directory if it doesn't exist
        base_dir.mkdir(parents=True, exist_ok=True)
        
        # Generate filename
        if request.custom_filename:
            filename = self._sanitize_filename(request.custom_filename)
        else:
            filename = self._sanitize_filename(metadata.title)
        
        # Add extension based on format
        if request.audio_only:
            extension = f".{request.audio_format.value}"
        else:
            extension = f".{request.video_format.value}"
        
        # Ensure unique filename
        output_path = base_dir / f"{filename}{extension}"
        counter = 1
        while output_path.exists():
            output_path = base_dir / f"{filename}_{counter}{extension}"
            counter += 1
        
        return output_path
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for filesystem compatibility."""
        # Remove or replace invalid characters
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # Remove leading/trailing whitespace and dots
        filename = filename.strip(' .')
        
        # Limit length
        if len(filename) > 200:
            filename = filename[:200]
        
        # Ensure not empty
        if not filename:
            filename = "video"
        
        return filename
    
    def _get_temp_path(self, final_path: Path) -> Path:
        """Get temporary path for download."""
        temp_dir = Path(self.config.storage.temp_dir)
        temp_dir.mkdir(parents=True, exist_ok=True)
        
        # Use same filename with .tmp extension
        temp_filename = f"{final_path.stem}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.tmp"
        return temp_dir / temp_filename
    
    def _parse_url(self, url: str) -> Dict[str, str]:
        """Parse URL and extract components."""
        parsed = urlparse(url)
        return {
            'scheme': parsed.scheme,
            'domain': parsed.netloc.lower(),
            'path': parsed.path,
            'query': parsed.query,
            'fragment': parsed.fragment,
            'full_url': url
        }
    
    def _validate_request(self, request: DownloadRequest):
        """Validate download request for this platform."""
        if not self.can_handle(str(request.url)):
            raise UnsupportedPlatformError(f"URL not supported by {self.__class__.__name__}")
        
        # Check if platform is enabled
        platform_config = self.config.get_platform_config(self.platform.value)
        if not platform_config or not platform_config.enabled:
            raise UnsupportedPlatformError(f"Platform {self.platform.value} is disabled")
    
    async def _post_process(
        self, 
        file_path: Path, 
        request: DownloadRequest,
        metadata: VideoMetadata
    ) -> Dict[str, Any]:
        """Post-process downloaded file."""
        result = {
            'file_path': str(file_path),
            'file_size': file_path.stat().st_size if file_path.exists() else 0,
            'metadata': metadata,
        }
        
        # Extract thumbnail if requested and available
        if request.extract_thumbnails and metadata.thumbnail_url:
            try:
                thumbnail_path = await self._download_thumbnail(
                    metadata.thumbnail_url, 
                    file_path.parent / f"{file_path.stem}_thumbnail.jpg"
                )
                result['thumbnail_path'] = str(thumbnail_path)
            except Exception as e:
                logger.warning(f"Failed to download thumbnail: {e}")
        
        return result
    
    async def _download_thumbnail(self, thumbnail_url: str, output_path: Path) -> Path:
        """Download video thumbnail."""
        import aiohttp
        
        async with aiohttp.ClientSession() as session:
            async with session.get(thumbnail_url) as response:
                if response.status == 200:
                    with open(output_path, 'wb') as f:
                        async for chunk in response.content.iter_chunked(8192):
                            f.write(chunk)
                    return output_path
                else:
                    raise DownloadError(f"Failed to download thumbnail: HTTP {response.status}")


class DownloaderRegistry:
    """Registry for managing downloaders."""
    
    def __init__(self):
        """Initialize downloader registry."""
        self.downloaders: List[BaseDownloader] = []
    
    def register(self, downloader: BaseDownloader):
        """Register a downloader."""
        self.downloaders.append(downloader)
        logger.info(f"Registered downloader: {downloader.__class__.__name__}")
    
    def get_downloader(self, url: str) -> Optional[BaseDownloader]:
        """Get appropriate downloader for URL."""
        for downloader in self.downloaders:
            if downloader.can_handle(url):
                return downloader
        return None
    
    def get_supported_platforms(self) -> List[Platform]:
        """Get list of supported platforms."""
        return [downloader.platform for downloader in self.downloaders if downloader.platform]
    
    def get_downloader_by_platform(self, platform: Platform) -> Optional[BaseDownloader]:
        """Get downloader by platform."""
        for downloader in self.downloaders:
            if downloader.platform == platform:
                return downloader
        return None


# Global downloader registry
_registry = DownloaderRegistry()


def get_downloader_registry() -> DownloaderRegistry:
    """Get the global downloader registry."""
    return _registry


def register_downloader(downloader: BaseDownloader):
    """Register a downloader with the global registry."""
    _registry.register(downloader)

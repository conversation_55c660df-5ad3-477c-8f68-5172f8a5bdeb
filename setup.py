#!/usr/bin/env python3
"""
Setup script for Video Downloader Service
"""

from setuptools import setup, find_packages, Extension
from pybind11.setup_helpers import Pybind11Extension, build_ext
from pybind11 import get_cmake_dir
import pybind11
import os
import sys

# Read version from __init__.py
def get_version():
    version_file = os.path.join("src", "python", "video_downloader", "__init__.py")
    if os.path.exists(version_file):
        with open(version_file, "r") as f:
            for line in f:
                if line.startswith("__version__"):
                    return line.split("=")[1].strip().strip('"').strip("'")
    return "1.0.0"

# Read long description from README
def get_long_description():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# Read requirements
def get_requirements():
    with open("requirements.txt", "r") as f:
        return [line.strip() for line in f if line.strip() and not line.startswith("#")]

# Define C++ extensions
ext_modules = [
    Pybind11Extension(
        "video_processor_cpp",
        [
            "src/cpp/video_processor/video_processor.cpp",
            "src/cpp/video_processor/format_converter.cpp",
            "src/cpp/video_processor/audio_extractor.cpp",
            "src/cpp/bindings/python_bindings.cpp",
        ],
        include_dirs=[
            "src/cpp/video_processor",
            pybind11.get_include(),
        ],
        libraries=["avcodec", "avformat", "avutil", "swscale", "swresample"],
        cxx_std=17,
        define_macros=[("VERSION_INFO", '"{}"'.format(get_version()))],
    ),
]

setup(
    name="video-downloader-service",
    version=get_version(),
    author="Video Downloader Team",
    author_email="<EMAIL>",
    description="Multi-platform video downloader service with Python/C++ hybrid architecture",
    long_description=get_long_description(),
    long_description_content_type="text/markdown",
    url="https://github.com/your-org/video-downloader-service",
    project_urls={
        "Bug Tracker": "https://github.com/your-org/video-downloader-service/issues",
        "Documentation": "https://video-downloader-service.readthedocs.io/",
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: C++",
        "Topic :: Multimedia :: Video",
        "Topic :: Internet :: WWW/HTTP",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    package_dir={"": "src/python"},
    packages=find_packages(where="src/python"),
    python_requires=">=3.8",
    install_requires=get_requirements(),
    extras_require={
        "dev": [
            "pytest>=7.0",
            "pytest-asyncio>=0.21.0",
            "pytest-cov>=4.0",
            "black>=23.0",
            "flake8>=6.0",
            "mypy>=1.0",
        ],
        "gui": [
            "streamlit>=1.28.0",
            "gradio>=4.8.0",
        ],
    },
    ext_modules=ext_modules,
    cmdclass={"build_ext": build_ext},
    zip_safe=False,
    include_package_data=True,
    package_data={
        "video_downloader": [
            "config/*.yaml",
            "templates/*.html",
            "static/*",
        ],
    },
    entry_points={
        "console_scripts": [
            "video-downloader=video_downloader.cli:main",
            "video-downloader-service=video_downloader.service:main",
        ],
    },
)

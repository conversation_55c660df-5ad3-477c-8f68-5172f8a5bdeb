cmake_minimum_required(VERSION 3.15)
project(video_downloader_cpp)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Find required packages
find_package(PkgConfig REQUIRED)

# Find FFmpeg libraries
pkg_check_modules(FFMPEG REQUIRED
    libavcodec
    libavformat
    libavutil
    libswscale
    libswresample
)

# Find pybind11
find_package(pybind11 REQUIRED)

# Include directories
include_directories(${FFMPEG_INCLUDE_DIRS})
include_directories(src/cpp/video_processor)

# Source files
set(SOURCES
    src/cpp/video_processor/video_processor.cpp
    src/cpp/video_processor/format_converter.cpp
    src/cpp/video_processor/audio_extractor.cpp
    src/cpp/video_processor/metadata_extractor.cpp
    src/cpp/video_processor/quality_selector.cpp
    src/cpp/bindings/python_bindings.cpp
)

# Create the Python module
pybind11_add_module(video_processor_cpp ${SOURCES})

# Link libraries
target_link_libraries(video_processor_cpp PRIVATE ${FFMPEG_LIBRARIES})

# Compiler-specific options
target_compile_definitions(video_processor_cpp PRIVATE VERSION_INFO=${EXAMPLE_VERSION_INFO})
target_compile_options(video_processor_cpp PRIVATE ${FFMPEG_CFLAGS_OTHER})

# Platform-specific settings
if(WIN32)
    # Windows-specific settings
    target_compile_definitions(video_processor_cpp PRIVATE _WIN32_WINNT=0x0601)
    if(MSVC)
        target_compile_options(video_processor_cpp PRIVATE /W4)
    endif()
elseif(UNIX)
    # Unix-specific settings
    target_compile_options(video_processor_cpp PRIVATE -Wall -Wextra -O3)
    if(APPLE)
        # macOS-specific settings
        set_target_properties(video_processor_cpp PROPERTIES
            INSTALL_RPATH "@loader_path"
        )
    else()
        # Linux-specific settings
        set_target_properties(video_processor_cpp PROPERTIES
            INSTALL_RPATH "$ORIGIN"
        )
    endif()
endif()

# Installation
install(TARGETS video_processor_cpp
    LIBRARY DESTINATION .
    RUNTIME DESTINATION .
)

# Optional: Build standalone executables for testing
option(BUILD_STANDALONE "Build standalone executables" OFF)

if(BUILD_STANDALONE)
    # Standalone video processor test
    add_executable(video_processor_test
        src/cpp/video_processor/video_processor.cpp
        src/cpp/video_processor/format_converter.cpp
        src/cpp/video_processor/audio_extractor.cpp
        src/cpp/video_processor/metadata_extractor.cpp
        src/cpp/video_processor/quality_selector.cpp
        src/cpp/tests/test_main.cpp
    )
    
    target_link_libraries(video_processor_test ${FFMPEG_LIBRARIES})
    target_compile_options(video_processor_test PRIVATE ${FFMPEG_CFLAGS_OTHER})
    
    install(TARGETS video_processor_test
        RUNTIME DESTINATION bin
    )
endif()

# Testing
enable_testing()

# Add custom target for building Python extension
add_custom_target(python_extension
    COMMAND ${CMAKE_COMMAND} --build . --target video_processor_cpp
    DEPENDS video_processor_cpp
    COMMENT "Building Python extension"
)

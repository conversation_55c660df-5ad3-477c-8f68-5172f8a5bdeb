"""
Advanced logging configuration for the video downloader service.
"""

import logging
import logging.handlers
import sys
import json
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
import structlog
from rich.logging import RichHandler
from rich.console import Console

from .config import get_config


class StructuredFormatter(logging.Formatter):
    """Custom formatter for structured logging."""
    
    def format(self, record):
        """Format log record as structured JSON."""
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # Add extra fields
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                          'filename', 'module', 'lineno', 'funcName', 'created',
                          'msecs', 'relativeCreated', 'thread', 'threadName',
                          'processName', 'process', 'getMessage', 'exc_info',
                          'exc_text', 'stack_info']:
                log_entry[key] = value
        
        return json.dumps(log_entry)


class VideoDownloaderLogger:
    """Enhanced logger for the video downloader service."""
    
    def __init__(self, config=None):
        """Initialize the logger."""
        self.config = config or get_config()
        self.console = Console()
        self._setup_logging()
    
    def _setup_logging(self):
        """Setup logging configuration."""
        # Clear existing handlers
        logging.getLogger().handlers.clear()
        
        # Set root logger level
        log_level = getattr(logging, self.config.logging.level.upper())
        logging.getLogger().setLevel(log_level)
        
        # Create logs directory
        logs_dir = Path(self.config.storage.logs_dir)
        logs_dir.mkdir(parents=True, exist_ok=True)
        
        # Setup console handler
        if self.config.logging.console_logging:
            self._setup_console_handler()
        
        # Setup file handlers
        if self.config.logging.file_logging:
            self._setup_file_handlers(logs_dir)
        
        # Setup structured logging if enabled
        if self.config.logging.structured_logging:
            self._setup_structured_logging()
    
    def _setup_console_handler(self):
        """Setup console logging handler."""
        if self.config.development.debug:
            # Use Rich handler for development
            console_handler = RichHandler(
                console=self.console,
                show_time=True,
                show_path=True,
                markup=True,
                rich_tracebacks=True
            )
            console_format = "%(message)s"
        else:
            # Use standard handler for production
            console_handler = logging.StreamHandler(sys.stdout)
            console_format = self.config.logging.format
        
        console_handler.setLevel(getattr(logging, self.config.logging.level.upper()))
        console_formatter = logging.Formatter(console_format)
        console_handler.setFormatter(console_formatter)
        
        logging.getLogger().addHandler(console_handler)
    
    def _setup_file_handlers(self, logs_dir: Path):
        """Setup file logging handlers."""
        # Main log file with rotation
        main_log_file = logs_dir / "service.log"
        file_handler = logging.handlers.RotatingFileHandler(
            main_log_file,
            maxBytes=self._parse_file_size(self.config.logging.max_file_size),
            backupCount=self.config.logging.backup_count
        )
        file_handler.setLevel(getattr(logging, self.config.logging.level.upper()))
        
        if self.config.logging.structured_logging:
            file_formatter = StructuredFormatter()
        else:
            file_formatter = logging.Formatter(self.config.logging.format)
        
        file_handler.setFormatter(file_formatter)
        logging.getLogger().addHandler(file_handler)
        
        # Error log file
        error_log_file = logs_dir / "error.log"
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=self._parse_file_size(self.config.logging.max_file_size),
            backupCount=self.config.logging.backup_count
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(file_formatter)
        logging.getLogger().addHandler(error_handler)
        
        # Download activity log
        download_log_file = logs_dir / "downloads.log"
        download_handler = logging.handlers.RotatingFileHandler(
            download_log_file,
            maxBytes=self._parse_file_size(self.config.logging.max_file_size),
            backupCount=self.config.logging.backup_count
        )
        download_handler.setLevel(logging.INFO)
        download_handler.setFormatter(file_formatter)
        
        # Create download logger
        download_logger = logging.getLogger('video_downloader.downloads')
        download_logger.addHandler(download_handler)
        download_logger.setLevel(logging.INFO)
        download_logger.propagate = False
    
    def _setup_structured_logging(self):
        """Setup structured logging with structlog."""
        structlog.configure(
            processors=[
                structlog.stdlib.filter_by_level,
                structlog.stdlib.add_logger_name,
                structlog.stdlib.add_log_level,
                structlog.stdlib.PositionalArgumentsFormatter(),
                structlog.processors.TimeStamper(fmt="iso"),
                structlog.processors.StackInfoRenderer(),
                structlog.processors.format_exc_info,
                structlog.processors.UnicodeDecoder(),
                structlog.processors.JSONRenderer()
            ],
            context_class=dict,
            logger_factory=structlog.stdlib.LoggerFactory(),
            wrapper_class=structlog.stdlib.BoundLogger,
            cache_logger_on_first_use=True,
        )
    
    def _parse_file_size(self, size_str: str) -> int:
        """Parse file size string to bytes."""
        size_str = size_str.upper()
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)
    
    def get_logger(self, name: str) -> logging.Logger:
        """Get a logger instance."""
        return logging.getLogger(name)
    
    def log_download_start(self, job_id: str, url: str, platform: str):
        """Log download start event."""
        download_logger = logging.getLogger('video_downloader.downloads')
        download_logger.info(
            "Download started",
            extra={
                'event': 'download_start',
                'job_id': job_id,
                'url': url,
                'platform': platform,
                'timestamp': datetime.now().isoformat()
            }
        )
    
    def log_download_complete(self, job_id: str, url: str, file_path: str, 
                            file_size: int, processing_time: float):
        """Log download completion event."""
        download_logger = logging.getLogger('video_downloader.downloads')
        download_logger.info(
            "Download completed",
            extra={
                'event': 'download_complete',
                'job_id': job_id,
                'url': url,
                'file_path': file_path,
                'file_size': file_size,
                'processing_time': processing_time,
                'timestamp': datetime.now().isoformat()
            }
        )
    
    def log_download_error(self, job_id: str, url: str, error: str, retry_count: int = 0):
        """Log download error event."""
        download_logger = logging.getLogger('video_downloader.downloads')
        download_logger.error(
            "Download failed",
            extra={
                'event': 'download_error',
                'job_id': job_id,
                'url': url,
                'error': error,
                'retry_count': retry_count,
                'timestamp': datetime.now().isoformat()
            }
        )
    
    def log_api_request(self, method: str, path: str, status_code: int, 
                       response_time: float, client_ip: str):
        """Log API request."""
        api_logger = logging.getLogger('video_downloader.api')
        api_logger.info(
            f"{method} {path} - {status_code}",
            extra={
                'event': 'api_request',
                'method': method,
                'path': path,
                'status_code': status_code,
                'response_time': response_time,
                'client_ip': client_ip,
                'timestamp': datetime.now().isoformat()
            }
        )


class DownloadMetrics:
    """Track download metrics and statistics."""
    
    def __init__(self):
        """Initialize metrics tracker."""
        self.metrics = {
            'total_downloads': 0,
            'successful_downloads': 0,
            'failed_downloads': 0,
            'total_bytes_downloaded': 0,
            'total_processing_time': 0.0,
            'platform_stats': {},
            'quality_stats': {},
            'format_stats': {},
            'error_stats': {},
        }
        self.logger = logging.getLogger('video_downloader.metrics')
    
    def record_download_start(self, platform: str, quality: str, format: str):
        """Record download start."""
        self.metrics['total_downloads'] += 1
        
        # Platform stats
        if platform not in self.metrics['platform_stats']:
            self.metrics['platform_stats'][platform] = {'total': 0, 'successful': 0, 'failed': 0}
        self.metrics['platform_stats'][platform]['total'] += 1
        
        # Quality stats
        self.metrics['quality_stats'][quality] = self.metrics['quality_stats'].get(quality, 0) + 1
        
        # Format stats
        self.metrics['format_stats'][format] = self.metrics['format_stats'].get(format, 0) + 1
    
    def record_download_success(self, platform: str, file_size: int, processing_time: float):
        """Record successful download."""
        self.metrics['successful_downloads'] += 1
        self.metrics['total_bytes_downloaded'] += file_size
        self.metrics['total_processing_time'] += processing_time
        
        if platform in self.metrics['platform_stats']:
            self.metrics['platform_stats'][platform]['successful'] += 1
    
    def record_download_failure(self, platform: str, error_type: str):
        """Record failed download."""
        self.metrics['failed_downloads'] += 1
        
        if platform in self.metrics['platform_stats']:
            self.metrics['platform_stats'][platform]['failed'] += 1
        
        # Error stats
        self.metrics['error_stats'][error_type] = self.metrics['error_stats'].get(error_type, 0) + 1
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current metrics."""
        return self.metrics.copy()
    
    def get_success_rate(self) -> float:
        """Get overall success rate."""
        if self.metrics['total_downloads'] == 0:
            return 0.0
        return (self.metrics['successful_downloads'] / self.metrics['total_downloads']) * 100
    
    def get_average_processing_time(self) -> float:
        """Get average processing time."""
        if self.metrics['successful_downloads'] == 0:
            return 0.0
        return self.metrics['total_processing_time'] / self.metrics['successful_downloads']
    
    def reset_metrics(self):
        """Reset all metrics."""
        self.metrics = {
            'total_downloads': 0,
            'successful_downloads': 0,
            'failed_downloads': 0,
            'total_bytes_downloaded': 0,
            'total_processing_time': 0.0,
            'platform_stats': {},
            'quality_stats': {},
            'format_stats': {},
            'error_stats': {},
        }
        self.logger.info("Metrics reset")


# Global instances
_logger_instance: Optional[VideoDownloaderLogger] = None
_metrics_instance: Optional[DownloadMetrics] = None


def get_logger_instance() -> VideoDownloaderLogger:
    """Get the global logger instance."""
    global _logger_instance
    if _logger_instance is None:
        _logger_instance = VideoDownloaderLogger()
    return _logger_instance


def get_metrics_instance() -> DownloadMetrics:
    """Get the global metrics instance."""
    global _metrics_instance
    if _metrics_instance is None:
        _metrics_instance = DownloadMetrics()
    return _metrics_instance


def setup_logging(config=None):
    """Setup logging for the application."""
    logger_instance = VideoDownloaderLogger(config)
    return logger_instance


def get_download_logger() -> logging.Logger:
    """Get the download logger."""
    return logging.getLogger('video_downloader.downloads')


def get_api_logger() -> logging.Logger:
    """Get the API logger."""
    return logging.getLogger('video_downloader.api')

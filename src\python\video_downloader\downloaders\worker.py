"""
Download worker that processes jobs from the queue.
"""

import asyncio
import logging
from datetime import datetime
from typing import Optional

from .factory import get_downloader_factory
from .base import ProgressCallback, DownloadError
from ..queue.job_queue import JobQueueManager, Job, JobStatus
from ..api.models import DownloadProgress, DownloadResult
from ..utils.config import Config


logger = logging.getLogger(__name__)


class DownloadWorker:
    """Worker that processes download jobs from the queue."""
    
    def __init__(self, worker_id: str, queue_manager: JobQueueManager, config: Config):
        """Initialize download worker."""
        self.worker_id = worker_id
        self.queue_manager = queue_manager
        self.config = config
        self.running = False
        self.current_job: Optional[Job] = None
        self.downloader_factory = get_downloader_factory()
        
        logger.info(f"Download worker {worker_id} initialized")
    
    async def start(self):
        """Start the worker."""
        if self.running:
            logger.warning(f"Worker {self.worker_id} is already running")
            return
        
        self.running = True
        logger.info(f"Starting download worker {self.worker_id}")
        
        try:
            while self.running:
                try:
                    # Get next job from queue
                    job = await self.queue_manager.queue.dequeue()
                    
                    if job:
                        await self._process_job(job)
                    else:
                        # No jobs available, wait a bit
                        await asyncio.sleep(1)
                        
                except Exception as e:
                    logger.error(f"Error in worker {self.worker_id}: {e}")
                    await asyncio.sleep(5)  # Wait before retrying
                    
        except asyncio.CancelledError:
            logger.info(f"Worker {self.worker_id} cancelled")
        finally:
            self.running = False
            logger.info(f"Worker {self.worker_id} stopped")
    
    async def stop(self):
        """Stop the worker."""
        logger.info(f"Stopping worker {self.worker_id}")
        self.running = False
        
        # Cancel current job if any
        if self.current_job:
            self.current_job.status = JobStatus.CANCELLED
            await self.queue_manager.queue.update_job(self.current_job)
    
    async def _process_job(self, job: Job):
        """Process a download job."""
        self.current_job = job
        start_time = datetime.now()
        
        logger.info(f"Worker {self.worker_id} processing job {job.id}")
        
        try:
            # Update job status to downloading
            job.status = JobStatus.DOWNLOADING
            job.updated_at = datetime.now()
            await self.queue_manager.queue.update_job(job)
            
            # Create progress callback
            progress_callback = ProgressCallback(
                job_id=job.id,
                update_callback=self._update_job_progress
            )
            
            # Get appropriate downloader
            downloader = self.downloader_factory.get_downloader(str(job.request.url))
            if not downloader:
                raise DownloadError(f"No downloader available for URL: {job.request.url}")
            
            # Download the video
            result = await downloader.download(job.request, progress_callback)
            
            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds()
            
            # Create download result
            download_result = DownloadResult(
                job_id=job.id,
                status=JobStatus.COMPLETED,
                metadata=result.get('metadata'),
                file_path=result.get('file_path'),
                file_size=result.get('file_size'),
                thumbnail_path=result.get('thumbnail_path'),
                subtitle_paths=result.get('subtitle_paths', []),
                processing_time=processing_time,
                created_at=job.created_at,
                completed_at=datetime.now()
            )
            
            # Update job with success
            job.status = JobStatus.COMPLETED
            job.result = download_result.model_dump()
            job.updated_at = datetime.now()
            await self.queue_manager.queue.update_job(job)
            
            logger.info(f"Job {job.id} completed successfully in {processing_time:.2f}s")
            
        except Exception as e:
            logger.error(f"Job {job.id} failed: {e}")
            
            # Update job with failure
            job.status = JobStatus.FAILED
            job.error_message = str(e)
            job.updated_at = datetime.now()
            await self.queue_manager.queue.update_job(job)
            
            # Check if we should retry
            if job.retry_count < job.max_retries:
                logger.info(f"Scheduling retry for job {job.id} (attempt {job.retry_count + 1})")
                await self.queue_manager.retry_job(job.id)
        
        finally:
            self.current_job = None
    
    async def _update_job_progress(self, progress: DownloadProgress):
        """Update job progress in the queue."""
        if not self.current_job:
            return
        
        try:
            # Update job progress
            self.current_job.progress = progress
            self.current_job.updated_at = datetime.now()
            await self.queue_manager.queue.update_job(self.current_job)
            
        except Exception as e:
            logger.error(f"Failed to update job progress: {e}")
    
    def get_status(self) -> dict:
        """Get worker status."""
        return {
            'worker_id': self.worker_id,
            'running': self.running,
            'current_job': self.current_job.id if self.current_job else None,
        }

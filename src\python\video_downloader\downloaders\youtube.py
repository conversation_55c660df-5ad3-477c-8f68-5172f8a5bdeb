"""
YouTube downloader implementation using yt-dlp.
"""

import asyncio
import logging
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List
import yt_dlp

from .base import BaseDownloader, ProgressCallback, DownloadError, VideoNotFoundError
from ..api.models import (
    DownloadRequest, VideoMetadata, Platform, VideoQuality, 
    AudioFormat, VideoFormat
)
from ..utils.config import Config


logger = logging.getLogger(__name__)


class YouTubeDownloader(BaseDownloader):
    """YouTube downloader using yt-dlp."""
    
    def __init__(self, config: Config):
        """Initialize YouTube downloader."""
        super().__init__(config)
        self.platform = Platform.YOUTUBE
        self.supported_domains = [
            'youtube.com', 'youtu.be', 'm.youtube.com', 
            'www.youtube.com', 'music.youtube.com'
        ]
    
    def can_handle(self, url: str) -> bool:
        """Check if this downloader can handle the given URL."""
        url_info = self._parse_url(url)
        return any(domain in url_info['domain'] for domain in self.supported_domains)
    
    async def extract_metadata(self, url: str) -> VideoMetadata:
        """Extract video metadata without downloading."""
        try:
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
                'extract_flat': False,
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                # Run in thread pool to avoid blocking
                loop = asyncio.get_event_loop()
                info = await loop.run_in_executor(
                    None, lambda: ydl.extract_info(url, download=False)
                )
            
            if not info:
                raise VideoNotFoundError(f"Could not extract info from URL: {url}")
            
            # Parse upload date
            upload_date = None
            if info.get('upload_date'):
                try:
                    upload_date = datetime.strptime(info['upload_date'], '%Y%m%d')
                except ValueError:
                    pass
            
            # Extract video ID
            video_id = info.get('id', '')
            
            # Create metadata object
            metadata = VideoMetadata(
                title=info.get('title', 'Unknown Title'),
                description=info.get('description', ''),
                duration=info.get('duration'),
                uploader=info.get('uploader', ''),
                upload_date=upload_date,
                view_count=info.get('view_count'),
                like_count=info.get('like_count'),
                thumbnail_url=info.get('thumbnail', ''),
                platform=self.platform,
                original_url=url,
                video_id=video_id,
                tags=info.get('tags', []) or [],
                categories=info.get('categories', []) or []
            )
            
            return metadata
            
        except Exception as e:
            logger.error(f"Failed to extract YouTube metadata: {e}")
            raise DownloadError(f"Failed to extract metadata: {str(e)}")
    
    async def get_available_qualities(self, url: str) -> List[VideoQuality]:
        """Get available video qualities for the URL."""
        try:
            ydl_opts = {
                'quiet': True,
                'no_warnings': True,
                'listformats': True,
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                loop = asyncio.get_event_loop()
                info = await loop.run_in_executor(
                    None, lambda: ydl.extract_info(url, download=False)
                )
            
            if not info or 'formats' not in info:
                return [VideoQuality.BEST]
            
            # Extract available qualities
            qualities = set()
            for fmt in info['formats']:
                if fmt.get('vcodec') != 'none':  # Has video
                    height = fmt.get('height')
                    if height:
                        if height >= 2160:
                            qualities.add(VideoQuality.Q2160P)
                        elif height >= 1440:
                            qualities.add(VideoQuality.Q1440P)
                        elif height >= 1080:
                            qualities.add(VideoQuality.Q1080P)
                        elif height >= 720:
                            qualities.add(VideoQuality.Q720P)
                        elif height >= 480:
                            qualities.add(VideoQuality.Q480P)
                        elif height >= 360:
                            qualities.add(VideoQuality.Q360P)
            
            # Always include best/worst options
            qualities.update([VideoQuality.BEST, VideoQuality.WORST, VideoQuality.AUDIO_ONLY])
            
            return list(qualities)
            
        except Exception as e:
            logger.error(f"Failed to get YouTube qualities: {e}")
            return [VideoQuality.BEST]
    
    async def get_available_formats(self, url: str, audio_only: bool = False) -> List[str]:
        """Get available formats for the URL."""
        if audio_only:
            return ['mp3', 'aac', 'opus', 'm4a']
        else:
            return ['mp4', 'webm', 'mkv']
    
    async def download(
        self, 
        request: DownloadRequest, 
        progress_callback: Optional[ProgressCallback] = None
    ) -> Dict[str, Any]:
        """Download video according to the request."""
        self._validate_request(request)
        
        try:
            # Extract metadata first
            metadata = await self.extract_metadata(str(request.url))
            
            # Get output path
            output_path = self._get_output_path(request, metadata)
            temp_path = self._get_temp_path(output_path)
            
            # Configure yt-dlp options
            ydl_opts = self._get_ydl_options(request, temp_path, progress_callback)
            
            # Download the video
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(
                    None, lambda: ydl.download([str(request.url)])
                )
            
            # Move from temp to final location
            if temp_path.exists():
                temp_path.rename(output_path)
            else:
                # yt-dlp might have used a different filename
                # Find the downloaded file in temp directory
                temp_files = list(temp_path.parent.glob(f"{temp_path.stem}*"))
                if temp_files:
                    temp_files[0].rename(output_path)
                else:
                    raise DownloadError("Downloaded file not found")
            
            # Post-process
            result = await self._post_process(output_path, request, metadata)
            
            logger.info(f"Successfully downloaded YouTube video: {metadata.title}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to download YouTube video: {e}")
            # Cleanup temp files
            if 'temp_path' in locals() and temp_path.exists():
                temp_path.unlink()
            raise DownloadError(f"Download failed: {str(e)}")
    
    def _get_ydl_options(
        self, 
        request: DownloadRequest, 
        output_path: Path,
        progress_callback: Optional[ProgressCallback] = None
    ) -> Dict[str, Any]:
        """Get yt-dlp options for the request."""
        opts = {
            'outtmpl': str(output_path.with_suffix('')),  # yt-dlp will add extension
            'quiet': True,
            'no_warnings': True,
        }
        
        # Quality selection
        if request.audio_only:
            opts['format'] = 'bestaudio/best'
            opts['postprocessors'] = [{
                'key': 'FFmpegExtractAudio',
                'preferredcodec': request.audio_format.value,
                'preferredquality': '192',
            }]
        else:
            # Video format selection
            format_selector = self._get_format_selector(request.quality, request.video_format)
            opts['format'] = format_selector
            
            # Post-processing for format conversion if needed
            if request.video_format != VideoFormat.MP4:
                opts['postprocessors'] = [{
                    'key': 'FFmpegVideoConvertor',
                    'preferedformat': request.video_format.value,
                }]
        
        # Subtitle extraction
        if request.extract_subtitles:
            opts['writesubtitles'] = True
            opts['writeautomaticsub'] = True
            opts['subtitleslangs'] = ['en', 'en-US']
        
        # Thumbnail extraction
        if request.extract_thumbnails:
            opts['writethumbnail'] = True
        
        # Progress hook
        if progress_callback:
            opts['progress_hooks'] = [self._create_progress_hook(progress_callback)]
        
        return opts
    
    def _get_format_selector(self, quality: VideoQuality, video_format: VideoFormat) -> str:
        """Get format selector string for yt-dlp."""
        if quality == VideoQuality.BEST:
            return f'best[ext={video_format.value}]/best'
        elif quality == VideoQuality.WORST:
            return f'worst[ext={video_format.value}]/worst'
        elif quality == VideoQuality.AUDIO_ONLY:
            return 'bestaudio/best'
        else:
            # Specific quality
            height_map = {
                VideoQuality.Q360P: 360,
                VideoQuality.Q480P: 480,
                VideoQuality.Q720P: 720,
                VideoQuality.Q1080P: 1080,
                VideoQuality.Q1440P: 1440,
                VideoQuality.Q2160P: 2160,
            }
            
            height = height_map.get(quality, 1080)
            return f'best[height<={height}][ext={video_format.value}]/best[height<={height}]/best'
    
    def _create_progress_hook(self, progress_callback: ProgressCallback):
        """Create progress hook for yt-dlp."""
        def hook(d):
            try:
                if d['status'] == 'downloading':
                    total_bytes = d.get('total_bytes') or d.get('total_bytes_estimate')
                    downloaded_bytes = d.get('downloaded_bytes', 0)
                    speed = d.get('speed')
                    eta = d.get('eta')
                    
                    progress_percent = 0.0
                    if total_bytes and total_bytes > 0:
                        progress_percent = (downloaded_bytes / total_bytes) * 100
                    
                    progress_data = {
                        'progress_percent': min(progress_percent, 100.0),
                        'downloaded_bytes': downloaded_bytes,
                        'total_bytes': total_bytes,
                        'download_speed': speed,
                        'eta': eta,
                        'current_stage': 'downloading'
                    }
                    
                    progress_callback(progress_data)
                    
                elif d['status'] == 'finished':
                    progress_data = {
                        'progress_percent': 100.0,
                        'downloaded_bytes': d.get('total_bytes', 0),
                        'total_bytes': d.get('total_bytes', 0),
                        'current_stage': 'finished'
                    }
                    progress_callback(progress_data)
                    
            except Exception as e:
                logger.error(f"Error in YouTube progress hook: {e}")
        
        return hook

#include "video_processor.h"
#include <iostream>
#include <sstream>
#include <algorithm>
#include <cmath>

extern "C" {
#include <libavutil/opt.h>
#include <libavutil/imgutils.h>
#include <libavutil/timestamp.h>
}

namespace video_processor {

VideoProcessor::VideoProcessor() : initialized_(false) {
}

VideoProcessor::~VideoProcessor() {
    cleanup();
}

bool VideoProcessor::initialize() {
    if (initialized_) {
        return true;
    }

    // Initialize FFmpeg libraries
    av_log_set_level(AV_LOG_WARNING);
    
    // Register all formats and codecs
    av_register_all();
    avformat_network_init();
    
    initialized_ = true;
    return true;
}

void VideoProcessor::cleanup() {
    if (initialized_) {
        avformat_network_deinit();
        initialized_ = false;
    }
}

VideoInfo VideoProcessor::extract_metadata(const std::string& input_path) {
    if (!initialized_) {
        throw VideoProcessorError("VideoProcessor not initialized");
    }

    VideoInfo info = {};
    info.filename = input_path;

    AVFormatContext* format_ctx = nullptr;
    int ret = avformat_open_input(&format_ctx, input_path.c_str(), nullptr, nullptr);
    if (ret < 0) {
        throw VideoProcessorError("Could not open input file: " + get_av_error_string(ret));
    }

    ret = avformat_find_stream_info(format_ctx, nullptr);
    if (ret < 0) {
        avformat_close_input(&format_ctx);
        throw VideoProcessorError("Could not find stream info: " + get_av_error_string(ret));
    }

    // Extract general info
    info.format = format_ctx->iformat->name;
    info.duration_ms = format_ctx->duration / (AV_TIME_BASE / 1000);
    info.bitrate = format_ctx->bit_rate;

    // Find video and audio streams
    for (unsigned int i = 0; i < format_ctx->nb_streams; i++) {
        AVStream* stream = format_ctx->streams[i];
        AVCodecParameters* codecpar = stream->codecpar;

        if (codecpar->codec_type == AVMEDIA_TYPE_VIDEO && info.width == 0) {
            info.width = codecpar->width;
            info.height = codecpar->height;
            info.video_codec = avcodec_get_name(codecpar->codec_id);
            
            // Calculate FPS
            if (stream->r_frame_rate.den > 0) {
                info.fps = static_cast<double>(stream->r_frame_rate.num) / stream->r_frame_rate.den;
            }
        }
        else if (codecpar->codec_type == AVMEDIA_TYPE_AUDIO && info.audio_codec.empty()) {
            info.audio_codec = avcodec_get_name(codecpar->codec_id);
            info.audio_sample_rate = codecpar->sample_rate;
            info.audio_channels = codecpar->channels;
        }
        else if (codecpar->codec_type == AVMEDIA_TYPE_SUBTITLE) {
            // Extract subtitle language if available
            AVDictionaryEntry* lang = av_dict_get(stream->metadata, "language", nullptr, 0);
            if (lang) {
                info.subtitle_languages.push_back(lang->value);
            } else {
                info.subtitle_languages.push_back("unknown");
            }
        }
    }

    avformat_close_input(&format_ctx);
    return info;
}

bool VideoProcessor::convert_video(
    const std::string& input_path,
    const std::string& output_path,
    const ConversionOptions& options,
    ProgressCallback progress_callback) {
    
    if (!initialized_) {
        log_error("VideoProcessor not initialized");
        return false;
    }

    AVFormatContext* input_ctx = nullptr;
    AVFormatContext* output_ctx = nullptr;
    
    try {
        // Open input file
        input_ctx = open_input_file(input_path);
        if (!input_ctx) {
            return false;
        }

        // Create output context
        output_ctx = create_output_context(output_path, options.output_format);
        if (!output_ctx) {
            avformat_close_input(&input_ctx);
            return false;
        }

        // Process streams
        bool success = true;
        int video_stream_idx = -1;
        int audio_stream_idx = -1;

        // Find video and audio streams
        for (unsigned int i = 0; i < input_ctx->nb_streams; i++) {
            AVStream* stream = input_ctx->streams[i];
            if (stream->codecpar->codec_type == AVMEDIA_TYPE_VIDEO && video_stream_idx == -1) {
                video_stream_idx = i;
            } else if (stream->codecpar->codec_type == AVMEDIA_TYPE_AUDIO && audio_stream_idx == -1) {
                audio_stream_idx = i;
            }
        }

        // Process video stream (unless audio-only)
        if (!options.audio_only && video_stream_idx >= 0) {
            if (progress_callback) {
                progress_callback(0.0, "Processing video stream");
            }
            success = process_video_stream(input_ctx, output_ctx, video_stream_idx, options, progress_callback);
        }

        // Process audio stream
        if (success && audio_stream_idx >= 0) {
            if (progress_callback) {
                progress_callback(options.audio_only ? 0.0 : 50.0, "Processing audio stream");
            }
            success = process_audio_stream(input_ctx, output_ctx, audio_stream_idx, options, progress_callback);
        }

        // Write trailer
        if (success) {
            av_write_trailer(output_ctx);
            if (progress_callback) {
                progress_callback(100.0, "Conversion completed");
            }
        }

        // Cleanup
        avformat_close_input(&input_ctx);
        if (output_ctx) {
            if (!(output_ctx->oformat->flags & AVFMT_NOFILE)) {
                avio_closep(&output_ctx->pb);
            }
            avformat_free_context(output_ctx);
        }

        return success;

    } catch (const std::exception& e) {
        log_error("Conversion failed: " + std::string(e.what()));
        
        // Cleanup on error
        if (input_ctx) {
            avformat_close_input(&input_ctx);
        }
        if (output_ctx) {
            if (!(output_ctx->oformat->flags & AVFMT_NOFILE)) {
                avio_closep(&output_ctx->pb);
            }
            avformat_free_context(output_ctx);
        }
        
        return false;
    }
}

bool VideoProcessor::extract_audio(
    const std::string& input_path,
    const std::string& output_path,
    const std::string& audio_format,
    int bitrate,
    ProgressCallback progress_callback) {
    
    ConversionOptions options;
    options.audio_only = true;
    options.output_format = audio_format;
    options.audio_bitrate = bitrate;
    
    return convert_video(input_path, output_path, options, progress_callback);
}

bool VideoProcessor::remove_watermark(
    const std::string& input_path,
    const std::string& output_path,
    const WatermarkRemovalOptions& options,
    ProgressCallback progress_callback) {
    
    // Create conversion options with cropping
    ConversionOptions conv_options;
    conv_options.output_format = utils::detect_format_from_extension(output_path);
    
    // For now, implement simple cropping
    // In a full implementation, you would use FFmpeg filters
    
    if (progress_callback) {
        progress_callback(0.0, "Removing watermark");
    }
    
    // This is a simplified implementation
    // A full implementation would use av_filter_graph for complex filtering
    bool success = convert_video(input_path, output_path, conv_options, 
        [&](double progress, const std::string& stage) {
            if (progress_callback) {
                progress_callback(progress, "Removing watermark: " + stage);
            }
        });
    
    return success;
}

bool VideoProcessor::generate_thumbnail(
    const std::string& input_path,
    const std::string& output_path,
    int64_t timestamp_ms,
    int width,
    int height) {
    
    if (!initialized_) {
        log_error("VideoProcessor not initialized");
        return false;
    }

    AVFormatContext* format_ctx = nullptr;
    AVCodecContext* codec_ctx = nullptr;
    AVFrame* frame = nullptr;
    AVFrame* rgb_frame = nullptr;
    SwsContext* sws_ctx = nullptr;

    try {
        // Open input file
        format_ctx = open_input_file(input_path);
        if (!format_ctx) {
            return false;
        }

        // Find video stream
        int video_stream_idx = -1;
        for (unsigned int i = 0; i < format_ctx->nb_streams; i++) {
            if (format_ctx->streams[i]->codecpar->codec_type == AVMEDIA_TYPE_VIDEO) {
                video_stream_idx = i;
                break;
            }
        }

        if (video_stream_idx == -1) {
            throw VideoProcessorError("No video stream found");
        }

        AVStream* video_stream = format_ctx->streams[video_stream_idx];
        
        // Create decoder context
        codec_ctx = create_decoder_context(video_stream);
        if (!codec_ctx) {
            throw VideoProcessorError("Failed to create decoder context");
        }

        // Seek to timestamp if specified
        if (timestamp_ms > 0) {
            int64_t seek_target = av_rescale_q(timestamp_ms, {1, 1000}, video_stream->time_base);
            av_seek_frame(format_ctx, video_stream_idx, seek_target, AVSEEK_FLAG_BACKWARD);
        }

        // Allocate frames
        frame = av_frame_alloc();
        rgb_frame = av_frame_alloc();
        if (!frame || !rgb_frame) {
            throw VideoProcessorError("Failed to allocate frames");
        }

        // Setup RGB frame
        rgb_frame->format = AV_PIX_FMT_RGB24;
        rgb_frame->width = width;
        rgb_frame->height = height;
        av_frame_get_buffer(rgb_frame, 32);

        // Create scaling context
        sws_ctx = sws_getContext(
            codec_ctx->width, codec_ctx->height, codec_ctx->pix_fmt,
            width, height, AV_PIX_FMT_RGB24,
            SWS_BILINEAR, nullptr, nullptr, nullptr
        );

        if (!sws_ctx) {
            throw VideoProcessorError("Failed to create scaling context");
        }

        // Read and decode frame
        AVPacket packet;
        av_init_packet(&packet);
        
        while (av_read_frame(format_ctx, &packet) >= 0) {
            if (packet.stream_index == video_stream_idx) {
                int ret = avcodec_send_packet(codec_ctx, &packet);
                if (ret >= 0) {
                    ret = avcodec_receive_frame(codec_ctx, frame);
                    if (ret >= 0) {
                        // Scale frame
                        sws_scale(sws_ctx, frame->data, frame->linesize, 0, codec_ctx->height,
                                rgb_frame->data, rgb_frame->linesize);
                        
                        // Save as image (simplified - would need image encoding)
                        // For now, just return success
                        av_packet_unref(&packet);
                        break;
                    }
                }
            }
            av_packet_unref(&packet);
        }

        // Cleanup
        if (sws_ctx) sws_freeContext(sws_ctx);
        if (frame) av_frame_free(&frame);
        if (rgb_frame) av_frame_free(&rgb_frame);
        if (codec_ctx) avcodec_free_context(&codec_ctx);
        if (format_ctx) avformat_close_input(&format_ctx);

        return true;

    } catch (const std::exception& e) {
        log_error("Thumbnail generation failed: " + std::string(e.what()));
        
        // Cleanup on error
        if (sws_ctx) sws_freeContext(sws_ctx);
        if (frame) av_frame_free(&frame);
        if (rgb_frame) av_frame_free(&rgb_frame);
        if (codec_ctx) avcodec_free_context(&codec_ctx);
        if (format_ctx) avformat_close_input(&format_ctx);
        
        return false;
    }
}

// Helper methods implementation
AVFormatContext* VideoProcessor::open_input_file(const std::string& filename) {
    AVFormatContext* format_ctx = nullptr;
    int ret = avformat_open_input(&format_ctx, filename.c_str(), nullptr, nullptr);
    if (ret < 0) {
        log_error("Could not open input file: " + filename, ret);
        return nullptr;
    }

    ret = avformat_find_stream_info(format_ctx, nullptr);
    if (ret < 0) {
        log_error("Could not find stream info", ret);
        avformat_close_input(&format_ctx);
        return nullptr;
    }

    return format_ctx;
}

AVFormatContext* VideoProcessor::create_output_context(const std::string& filename, const std::string& format) {
    AVFormatContext* output_ctx = nullptr;
    int ret = avformat_alloc_output_context2(&output_ctx, nullptr, format.c_str(), filename.c_str());
    if (ret < 0) {
        log_error("Could not create output context", ret);
        return nullptr;
    }

    if (!(output_ctx->oformat->flags & AVFMT_NOFILE)) {
        ret = avio_open(&output_ctx->pb, filename.c_str(), AVIO_FLAG_WRITE);
        if (ret < 0) {
            log_error("Could not open output file: " + filename, ret);
            avformat_free_context(output_ctx);
            return nullptr;
        }
    }

    return output_ctx;
}

std::string VideoProcessor::get_av_error_string(int error_code) {
    char error_buf[AV_ERROR_MAX_STRING_SIZE];
    av_strerror(error_code, error_buf, AV_ERROR_MAX_STRING_SIZE);
    return std::string(error_buf);
}

void VideoProcessor::log_error(const std::string& message, int av_error) {
    std::string full_message = message;
    if (av_error != 0) {
        full_message += ": " + get_av_error_string(av_error);
    }
    std::cerr << "VideoProcessor Error: " << full_message << std::endl;
}

// Utility functions
namespace utils {
    std::string format_duration(int64_t duration_ms) {
        int hours = duration_ms / (1000 * 60 * 60);
        int minutes = (duration_ms % (1000 * 60 * 60)) / (1000 * 60);
        int seconds = (duration_ms % (1000 * 60)) / 1000;
        
        std::ostringstream oss;
        oss << hours << ":" << std::setfill('0') << std::setw(2) << minutes 
            << ":" << std::setw(2) << seconds;
        return oss.str();
    }

    std::string detect_format_from_extension(const std::string& filename) {
        size_t dot_pos = filename.find_last_of('.');
        if (dot_pos == std::string::npos) {
            return "mp4";  // Default format
        }
        
        std::string ext = filename.substr(dot_pos + 1);
        std::transform(ext.begin(), ext.end(), ext.begin(), ::tolower);
        
        if (ext == "mp4") return "mp4";
        if (ext == "avi") return "avi";
        if (ext == "mkv") return "matroska";
        if (ext == "webm") return "webm";
        if (ext == "mp3") return "mp3";
        if (ext == "aac") return "aac";
        if (ext == "wav") return "wav";
        
        return "mp4";  // Default
    }
}

} // namespace video_processor

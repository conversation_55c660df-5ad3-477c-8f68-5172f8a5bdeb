#!/usr/bin/env python3
"""
Installation script for the Video Downloader Service.
"""

import os
import sys
import subprocess
import platform
import shutil
from pathlib import Path


def run_command(cmd, check=True, shell=False):
    """Run a command and return the result."""
    print(f"Running: {cmd}")
    if isinstance(cmd, str) and not shell:
        cmd = cmd.split()
    
    result = subprocess.run(cmd, capture_output=True, text=True, shell=shell)
    
    if check and result.returncode != 0:
        print(f"Error running command: {cmd}")
        print(f"stdout: {result.stdout}")
        print(f"stderr: {result.stderr}")
        sys.exit(1)
    
    return result


def check_python_version():
    """Check if Python version is compatible."""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("Error: Python 3.8 or higher is required")
        sys.exit(1)
    
    print(f"✓ Python {version.major}.{version.minor}.{version.micro} detected")


def check_system_dependencies():
    """Check for required system dependencies."""
    print("Checking system dependencies...")
    
    # Check for FFmpeg
    ffmpeg_path = shutil.which("ffmpeg")
    if not ffmpeg_path:
        print("Warning: FFmpeg not found. Please install FFmpeg for video processing.")
        print("  Ubuntu/Debian: sudo apt install ffmpeg")
        print("  macOS: brew install ffmpeg")
        print("  Windows: Download from https://ffmpeg.org/download.html")
    else:
        print(f"✓ FFmpeg found at {ffmpeg_path}")
    
    # Check for Git
    git_path = shutil.which("git")
    if not git_path:
        print("Warning: Git not found. Some features may not work.")
    else:
        print(f"✓ Git found at {git_path}")
    
    # Check for C++ compiler (for building C++ extensions)
    system = platform.system()
    if system == "Windows":
        # Check for MSVC or MinGW
        cl_path = shutil.which("cl")
        gcc_path = shutil.which("gcc")
        if not cl_path and not gcc_path:
            print("Warning: No C++ compiler found. C++ extensions will not be built.")
            print("  Install Visual Studio Build Tools or MinGW-w64")
        else:
            compiler = "MSVC" if cl_path else "GCC"
            print(f"✓ C++ compiler found: {compiler}")
    else:
        # Check for GCC or Clang
        gcc_path = shutil.which("gcc")
        clang_path = shutil.which("clang")
        if not gcc_path and not clang_path:
            print("Warning: No C++ compiler found. C++ extensions will not be built.")
            print("  Ubuntu/Debian: sudo apt install build-essential")
            print("  macOS: xcode-select --install")
        else:
            compiler = "GCC" if gcc_path else "Clang"
            print(f"✓ C++ compiler found: {compiler}")


def install_python_dependencies():
    """Install Python dependencies."""
    print("Installing Python dependencies...")
    
    # Upgrade pip
    run_command([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
    
    # Install requirements
    requirements_file = Path(__file__).parent.parent / "requirements.txt"
    if requirements_file.exists():
        run_command([sys.executable, "-m", "pip", "install", "-r", str(requirements_file)])
    else:
        print("Warning: requirements.txt not found")
    
    print("✓ Python dependencies installed")


def build_cpp_extensions():
    """Build C++ extensions."""
    print("Building C++ extensions...")
    
    project_root = Path(__file__).parent.parent
    build_dir = project_root / "build"
    
    try:
        # Create build directory
        build_dir.mkdir(exist_ok=True)
        
        # Run CMake
        os.chdir(build_dir)
        run_command(["cmake", ".."])
        
        # Build
        run_command(["cmake", "--build", ".", "--config", "Release"])
        
        print("✓ C++ extensions built successfully")
        
    except Exception as e:
        print(f"Warning: Failed to build C++ extensions: {e}")
        print("The service will work without C++ optimizations")
    finally:
        os.chdir(project_root)


def install_service():
    """Install the service package."""
    print("Installing video downloader service...")
    
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    
    # Install in development mode
    run_command([sys.executable, "-m", "pip", "install", "-e", "."])
    
    print("✓ Service installed")


def create_directories():
    """Create necessary directories."""
    print("Creating directories...")
    
    directories = [
        "downloads",
        "temp", 
        "logs",
        "config"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✓ Created directory: {directory}")


def setup_configuration():
    """Setup initial configuration."""
    print("Setting up configuration...")
    
    config_file = Path("config/settings.yaml")
    if config_file.exists():
        print("✓ Configuration file already exists")
        return
    
    # Copy default configuration
    default_config = Path(__file__).parent.parent / "config" / "settings.yaml"
    if default_config.exists():
        shutil.copy2(default_config, config_file)
        print("✓ Default configuration copied")
    else:
        print("Warning: Default configuration not found")


def run_tests():
    """Run basic tests to verify installation."""
    print("Running basic tests...")
    
    try:
        # Test import
        import video_downloader
        print("✓ Package import successful")
        
        # Test CLI
        result = run_command([sys.executable, "-m", "video_downloader.cli", "--help"], check=False)
        if result.returncode == 0:
            print("✓ CLI working")
        else:
            print("Warning: CLI test failed")
        
    except ImportError as e:
        print(f"Warning: Package import failed: {e}")


def main():
    """Main installation function."""
    print("Video Downloader Service Installation")
    print("=" * 40)
    
    # Check prerequisites
    check_python_version()
    check_system_dependencies()
    
    # Install components
    install_python_dependencies()
    create_directories()
    setup_configuration()
    
    # Build C++ extensions (optional)
    try:
        build_cpp_extensions()
    except Exception as e:
        print(f"Skipping C++ extensions: {e}")
    
    # Install service
    install_service()
    
    # Run tests
    run_tests()
    
    print("\n" + "=" * 40)
    print("Installation completed!")
    print("\nNext steps:")
    print("1. Review configuration in config/settings.yaml")
    print("2. Start the service: video-downloader serve")
    print("3. Or use CLI: video-downloader download <URL>")
    print("4. Check health: video-downloader health")
    print("\nFor more information, see README.md")


if __name__ == "__main__":
    main()

"""
Web interface routes for the video downloader service.
"""

import logging
from fastapi import APIRouter, Request, Form, HTTPException
from fastapi.responses import HTMLResponse, RedirectResponse
from fastapi.templating import Jinja2Templates
from pathlib import Path

from ..models import DownloadRequest, VideoQuality, AudioFormat, VideoFormat
from ...queue.job_queue import get_job_queue_manager
from ...utils.config import get_config


logger = logging.getLogger(__name__)

# Setup templates
template_dir = Path(__file__).parent.parent.parent.parent.parent / "templates"
templates = Jinja2Templates(directory=str(template_dir))

web_router = APIRouter()


@web_router.get("/web", response_class=HTMLResponse)
async def web_interface(request: Request):
    """Main web interface page."""
    try:
        config = get_config()
        queue_manager = get_job_queue_manager()
        
        # Get queue stats
        stats = await queue_manager.get_queue_stats()
        
        # Get recent jobs
        recent_jobs = await queue_manager.queue.get_completed_jobs(limit=10)
        
        context = {
            "request": request,
            "service_name": config.service.name,
            "version": config.service.version,
            "queue_size": stats.get("queue_size", 0),
            "active_jobs": stats.get("active_jobs", 0),
            "recent_jobs": recent_jobs[:5],  # Show only 5 most recent
            "qualities": [q.value for q in VideoQuality],
            "audio_formats": [f.value for f in AudioFormat],
            "video_formats": [f.value for f in VideoFormat]
        }
        
        return templates.TemplateResponse("index.html", context)
        
    except Exception as e:
        logger.error(f"Error loading web interface: {e}")
        # Return simple HTML if template fails
        return HTMLResponse(f"""
        <html>
            <head><title>Video Downloader Service</title></head>
            <body>
                <h1>Video Downloader Service</h1>
                <p>Error loading interface: {e}</p>
                <p>Please use the API directly or check the logs.</p>
            </body>
        </html>
        """)


@web_router.post("/web/download")
async def web_submit_download(
    request: Request,
    url: str = Form(...),
    quality: str = Form("best"),
    audio_only: bool = Form(False),
    audio_format: str = Form("mp3"),
    video_format: str = Form("mp4"),
    extract_thumbnails: bool = Form(True),
    extract_subtitles: bool = Form(False),
    remove_watermark: bool = Form(True)
):
    """Submit download via web form."""
    try:
        # Create download request
        download_request = DownloadRequest(
            url=url,
            quality=VideoQuality(quality),
            audio_only=audio_only,
            audio_format=AudioFormat(audio_format),
            video_format=VideoFormat(video_format),
            extract_thumbnails=extract_thumbnails,
            extract_subtitles=extract_subtitles,
            remove_watermark=remove_watermark
        )
        
        # Submit to queue
        queue_manager = get_job_queue_manager()
        job_id = await queue_manager.submit_job(download_request)
        
        # Redirect to status page
        return RedirectResponse(url=f"/web/status/{job_id}", status_code=303)
        
    except Exception as e:
        logger.error(f"Error submitting download: {e}")
        raise HTTPException(status_code=400, detail=str(e))


@web_router.get("/web/status/{job_id}", response_class=HTMLResponse)
async def web_job_status(request: Request, job_id: str):
    """Job status page."""
    try:
        queue_manager = get_job_queue_manager()
        job = await queue_manager.get_job_status(job_id)
        
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")
        
        context = {
            "request": request,
            "job": job,
            "job_id": job_id
        }
        
        return templates.TemplateResponse("status.html", context)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error loading job status: {e}")
        return HTMLResponse(f"""
        <html>
            <head><title>Job Status - Error</title></head>
            <body>
                <h1>Error Loading Job Status</h1>
                <p>Job ID: {job_id}</p>
                <p>Error: {e}</p>
                <a href="/web">Back to Home</a>
            </body>
        </html>
        """)


@web_router.get("/web/jobs", response_class=HTMLResponse)
async def web_job_list(request: Request):
    """Job list page."""
    try:
        queue_manager = get_job_queue_manager()
        
        # Get recent jobs
        recent_jobs = await queue_manager.queue.get_completed_jobs(limit=50)
        active_jobs = await queue_manager.queue.get_active_jobs()
        
        context = {
            "request": request,
            "recent_jobs": recent_jobs,
            "active_jobs": active_jobs
        }
        
        return templates.TemplateResponse("jobs.html", context)
        
    except Exception as e:
        logger.error(f"Error loading job list: {e}")
        return HTMLResponse(f"""
        <html>
            <head><title>Jobs - Error</title></head>
            <body>
                <h1>Error Loading Jobs</h1>
                <p>Error: {e}</p>
                <a href="/web">Back to Home</a>
            </body>
        </html>
        """)


@web_router.get("/web/api-docs", response_class=HTMLResponse)
async def web_api_docs(request: Request):
    """API documentation page."""
    config = get_config()
    
    return HTMLResponse(f"""
    <html>
        <head>
            <title>API Documentation - Video Downloader Service</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; }}
                .endpoint {{ margin: 20px 0; padding: 15px; border: 1px solid #ddd; }}
                .method {{ font-weight: bold; color: #007bff; }}
                .url {{ font-family: monospace; background: #f8f9fa; padding: 2px 4px; }}
                pre {{ background: #f8f9fa; padding: 10px; overflow-x: auto; }}
            </style>
        </head>
        <body>
            <h1>Video Downloader Service API</h1>
            <p>Version: {config.service.version}</p>
            
            <h2>Download Endpoints</h2>
            
            <div class="endpoint">
                <div class="method">POST</div>
                <div class="url">/api/download</div>
                <p>Submit a video download request</p>
                <pre>{{
    "url": "https://www.youtube.com/watch?v=example",
    "quality": "1080p",
    "audio_only": false,
    "video_format": "mp4"
}}</pre>
            </div>
            
            <div class="endpoint">
                <div class="method">GET</div>
                <div class="url">/api/status/{{job_id}}</div>
                <p>Get download job status</p>
            </div>
            
            <div class="endpoint">
                <div class="method">GET</div>
                <div class="url">/api/downloads</div>
                <p>List completed downloads</p>
            </div>
            
            <div class="endpoint">
                <div class="method">GET</div>
                <div class="url">/health</div>
                <p>Service health check</p>
            </div>
            
            <h2>Interactive Documentation</h2>
            <p><a href="/docs">Swagger UI</a> - Interactive API documentation</p>
            <p><a href="/redoc">ReDoc</a> - Alternative API documentation</p>
            
            <p><a href="/web">Back to Home</a></p>
        </body>
    </html>
    """)


# API endpoint for web interface to get job status (AJAX)
@web_router.get("/web/api/status/{job_id}")
async def web_api_job_status(job_id: str):
    """Get job status for web interface (AJAX endpoint)."""
    try:
        queue_manager = get_job_queue_manager()
        job = await queue_manager.get_job_status(job_id)
        
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")
        
        # Return simplified status for web interface
        status_data = {
            "job_id": job.id,
            "status": job.status.value,
            "created_at": job.created_at.isoformat(),
            "updated_at": job.updated_at.isoformat()
        }
        
        if job.progress:
            status_data["progress"] = {
                "percent": job.progress.progress_percent,
                "stage": job.progress.current_stage,
                "speed": job.progress.download_speed,
                "eta": job.progress.eta
            }
        
        if job.result:
            status_data["result"] = {
                "file_path": job.result.get("file_path"),
                "file_size": job.result.get("file_size"),
                "processing_time": job.result.get("processing_time")
            }
        
        if job.error_message:
            status_data["error"] = job.error_message
        
        return status_data
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

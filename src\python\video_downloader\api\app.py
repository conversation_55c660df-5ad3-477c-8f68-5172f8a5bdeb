"""
FastAPI application setup and configuration.
"""

import logging
from datetime import datetime
from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from .routes import download, status, admin
from .routes.web import web_router
from .middleware import rate_limit_middleware, error_handler_middleware
from ..utils.config import get_config
from ..downloaders.factory import get_downloader_factory


logger = logging.getLogger(__name__)


def create_app() -> FastAPI:
    """Create and configure FastAPI application."""
    config = get_config()
    
    # Create FastAPI app
    app = FastAPI(
        title=config.api.title,
        description=config.api.description,
        version=config.api.version,
        docs_url=config.api.docs_url,
        redoc_url=config.api.redoc_url,
    )
    
    # Add CORS middleware
    if config.api.cors_enabled:
        app.add_middleware(
            CORSMiddleware,
            allow_origins=config.api.cors_origins,
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    # Add custom middleware
    app.middleware("http")(error_handler_middleware)
    app.middleware("http")(rate_limit_middleware)
    
    # Include routers
    app.include_router(download.router, prefix="/api", tags=["download"])
    app.include_router(status.router, prefix="/api", tags=["status"])
    app.include_router(admin.router, prefix="/api/admin", tags=["admin"])
    app.include_router(web_router, prefix="", tags=["web"])
    
    # Health check endpoint
    @app.get("/health")
    async def health_check():
        """Health check endpoint."""
        return {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "version": config.service.version
        }
    
    # Root endpoint
    @app.get("/")
    async def root():
        """Root endpoint."""
        return {
            "message": "Video Downloader Service",
            "version": config.service.version,
            "docs": config.api.docs_url,
            "health": "/health"
        }
    
    # Startup event
    @app.on_event("startup")
    async def startup_event():
        """Application startup event."""
        logger.info("Starting Video Downloader API")
        
        # Initialize downloader factory
        factory = get_downloader_factory()
        factory.initialize()
        
        logger.info("Video Downloader API started successfully")
    
    # Shutdown event
    @app.on_event("shutdown")
    async def shutdown_event():
        """Application shutdown event."""
        logger.info("Shutting down Video Downloader API")
    
    return app


def run_server(host: str = "0.0.0.0", port: int = 8080, reload: bool = False):
    """Run the FastAPI server."""
    uvicorn.run(
        "video_downloader.api.app:create_app",
        host=host,
        port=port,
        reload=reload,
        factory=True
    )


if __name__ == "__main__":
    config = get_config()
    run_server(
        host=config.service.host,
        port=config.service.port,
        reload=config.development.reload
    )

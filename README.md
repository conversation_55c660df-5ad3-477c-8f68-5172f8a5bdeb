# Multi-Platform Video Downloader Service

A high-performance Python/C++ hybrid application that serves as a background daemon for downloading videos from multiple platforms including YouTube, TikTok, and Facebook.

## Features

### Core Functionality
- **Multi-platform support**: Download videos from Facebook, TikTok, and YouTube
- **Quality options**: Support for multiple video quality options (720p, 1080p, 4K when available)
- **Audio extraction**: Download audio-only files in MP3, AAC formats
- **TikTok watermark removal**: Download TikTok videos without watermarks
- **Background service**: Runs as a daemon/service processing requests continuously

### Technical Features
- **Hybrid Architecture**: Python service orchestration with C++ video processing
- **REST API**: HTTP endpoints for submitting download requests
- **Job Queue**: Asynchronous processing of multiple download requests
- **Progress Tracking**: Real-time download progress monitoring
- **Error Handling**: Robust error handling and retry mechanisms
- **File Management**: Organized storage with metadata preservation

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Python Service  │───▶│ C++ Video        │───▶│ Python API &    │
│   Controller    │    │ Processing Core  │    │ File Management │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Job Queue &     │    │ FFmpeg & Video   │    │ Download Cache  │
│ Progress Track  │    │ Format Engine    │    │ & Metadata DB   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## Installation

### Prerequisites
- Python 3.8+
- C++ compiler (GCC/Clang/MSVC)
- FFmpeg
- CMake
- Git

### Setup
```bash
# Clone the repository
git clone <repository-url>
cd video-downloader-service

# Install Python dependencies
pip install -r requirements.txt

# Build C++ components
mkdir build && cd build
cmake ..
make

# Install the service
python setup.py install
```

## Usage

### Starting the Service
```bash
# Start as daemon
python -m video_downloader.service --daemon

# Start with console output
python -m video_downloader.service --console
```

### API Usage
```bash
# Submit download request
curl -X POST http://localhost:8080/api/download \
  -H "Content-Type: application/json" \
  -d '{
    "url": "https://www.youtube.com/watch?v=example",
    "quality": "1080p",
    "format": "mp4",
    "audio_only": false
  }'

# Check download progress
curl http://localhost:8080/api/status/{job_id}

# List completed downloads
curl http://localhost:8080/api/downloads
```

### CLI Usage
```bash
# Download single video
video-downloader download "https://www.youtube.com/watch?v=example" --quality 1080p

# Download audio only
video-downloader download "https://tiktok.com/@user/video/123" --audio-only --format mp3

# Batch download from file
video-downloader batch urls.txt --output-dir ./downloads
```

## Configuration

Configuration is managed through `config/settings.yaml`:

```yaml
service:
  host: "0.0.0.0"
  port: 8080
  workers: 4
  max_concurrent_downloads: 10

storage:
  download_dir: "./downloads"
  temp_dir: "./temp"
  max_file_size: "2GB"

platforms:
  youtube:
    enabled: true
    max_quality: "4K"
  tiktok:
    enabled: true
    remove_watermark: true
  facebook:
    enabled: true
    max_quality: "1080p"

processing:
  use_cpp_engine: true
  ffmpeg_path: "/usr/bin/ffmpeg"
  concurrent_conversions: 2
```

## Development

### Project Structure
```
video-downloader-service/
├── src/
│   ├── python/
│   │   ├── video_downloader/
│   │   │   ├── service/          # Main service components
│   │   │   ├── downloaders/      # Platform-specific downloaders
│   │   │   ├── api/              # REST API endpoints
│   │   │   ├── queue/            # Job queue management
│   │   │   └── utils/            # Utilities and helpers
│   │   └── tests/                # Python tests
│   └── cpp/
│       ├── video_processor/      # C++ video processing
│       ├── bindings/             # Python bindings
│       └── tests/                # C++ tests
├── config/                       # Configuration files
├── docs/                         # Documentation
└── scripts/                      # Build and deployment scripts
```

## License

MIT License - see LICENSE file for details.

"""
Downloader factory and registry management.
"""

import logging
from typing import Optional, List, Dict, Any

from .base import BaseDownloader, get_downloader_registry, register_downloader
from .youtube import YouTubeDownloader
from .tiktok import TikTokDownloader
from ..api.models import Platform, DownloadRequest
from ..utils.config import get_config


logger = logging.getLogger(__name__)


class DownloaderFactory:
    """Factory class for creating and managing downloaders."""
    
    def __init__(self):
        """Initialize downloader factory."""
        self.config = get_config()
        self._initialized = False
    
    def initialize(self):
        """Initialize all downloaders."""
        if self._initialized:
            return
        
        try:
            # Register YouTube downloader
            if self.config.is_platform_enabled('youtube'):
                youtube_downloader = YouTubeDownloader(self.config)
                register_downloader(youtube_downloader)
                logger.info("YouTube downloader registered")
            
            # Register TikTok downloader
            if self.config.is_platform_enabled('tiktok'):
                tiktok_downloader = TikTokDownloader(self.config)
                register_downloader(tiktok_downloader)
                logger.info("TikTok downloader registered")
            
            # Register Facebook downloader (placeholder for now)
            # if self.config.is_platform_enabled('facebook'):
            #     facebook_downloader = FacebookDownloader(self.config)
            #     register_downloader(facebook_downloader)
            #     logger.info("Facebook downloader registered")
            
            self._initialized = True
            logger.info("All downloaders initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize downloaders: {e}")
            raise
    
    def get_downloader(self, url: str) -> Optional[BaseDownloader]:
        """Get appropriate downloader for URL."""
        if not self._initialized:
            self.initialize()
        
        registry = get_downloader_registry()
        return registry.get_downloader(url)
    
    def get_downloader_by_platform(self, platform: Platform) -> Optional[BaseDownloader]:
        """Get downloader by platform."""
        if not self._initialized:
            self.initialize()
        
        registry = get_downloader_registry()
        return registry.get_downloader_by_platform(platform)
    
    def get_supported_platforms(self) -> List[Platform]:
        """Get list of supported platforms."""
        if not self._initialized:
            self.initialize()
        
        registry = get_downloader_registry()
        return registry.get_supported_platforms()
    
    def validate_request(self, request: DownloadRequest) -> bool:
        """Validate if a download request can be handled."""
        downloader = self.get_downloader(str(request.url))
        return downloader is not None
    
    def get_platform_capabilities(self, platform: Platform) -> Dict[str, Any]:
        """Get capabilities for a specific platform."""
        downloader = self.get_downloader_by_platform(platform)
        if not downloader:
            return {}
        
        platform_config = self.config.get_platform_config(platform.value)
        if not platform_config:
            return {}
        
        return {
            'enabled': platform_config.enabled,
            'max_quality': platform_config.max_quality,
            'audio_formats': platform_config.audio_formats,
            'video_formats': platform_config.video_formats,
            'extract_thumbnails': platform_config.extract_thumbnails,
            'features': {
                'watermark_removal': platform == Platform.TIKTOK and 
                                   getattr(platform_config, 'remove_watermark', False),
                'subtitle_extraction': platform == Platform.YOUTUBE,
                'live_streams': platform == Platform.YOUTUBE,
            }
        }


# Global factory instance
_factory_instance: Optional[DownloaderFactory] = None


def get_downloader_factory() -> DownloaderFactory:
    """Get the global downloader factory instance."""
    global _factory_instance
    if _factory_instance is None:
        _factory_instance = DownloaderFactory()
    return _factory_instance

"""
Configuration management for the video downloader service.
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from pydantic import Field
from pydantic_settings import BaseSettings
from pydantic_settings import SettingsConfigDict


class ServiceConfig(BaseSettings):
    """Service configuration settings."""
    name: str = "video-downloader-service"
    version: str = "1.0.0"
    host: str = "0.0.0.0"
    port: int = 8080
    workers: int = 4
    max_concurrent_downloads: int = 10
    daemon_mode: bool = False
    pid_file: str = "./logs/service.pid"


class APIConfig(BaseSettings):
    """API configuration settings."""
    title: str = "Video Downloader API"
    description: str = "Multi-platform video downloader service"
    version: str = "1.0.0"
    docs_url: str = "/docs"
    redoc_url: str = "/redoc"
    cors_enabled: bool = True
    cors_origins: list = ["*"]
    rate_limit: str = "100/minute"


class StorageConfig(BaseSettings):
    """Storage configuration settings."""
    download_dir: str = "./downloads"
    temp_dir: str = "./temp"
    logs_dir: str = "./logs"
    max_file_size: str = "2GB"
    cleanup_temp_files: bool = True
    cleanup_interval: int = 3600
    organize_by_platform: bool = True
    organize_by_date: bool = True


class DatabaseConfig(BaseSettings):
    """Database configuration settings."""
    url: str = "sqlite:///./video_downloader.db"
    echo: bool = False
    pool_size: int = 10
    max_overflow: int = 20


class QueueConfig(BaseSettings):
    """Queue configuration settings."""
    backend: str = "redis"
    redis_url: str = "redis://localhost:6379/0"
    max_retries: int = 3
    retry_delay: int = 60
    job_timeout: int = 3600
    result_ttl: int = 86400


class PlatformConfig(BaseSettings):
    """Platform-specific configuration."""
    enabled: bool = True
    max_quality: str = "1080p"
    audio_formats: list = ["mp3", "aac"]
    video_formats: list = ["mp4"]
    extract_thumbnails: bool = True


class ProcessingConfig(BaseSettings):
    """Video processing configuration."""
    use_cpp_engine: bool = True
    ffmpeg_path: str = "ffmpeg"
    concurrent_conversions: int = 2
    temp_cleanup: bool = True
    preserve_metadata: bool = True
    quality_presets: Dict[str, str] = {
        "4K": "2160p",
        "1080p": "1080p", 
        "720p": "720p",
        "480p": "480p",
        "360p": "360p",
        "audio_only": "bestaudio"
    }


class LoggingConfig(BaseSettings):
    """Logging configuration."""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_logging: bool = True
    console_logging: bool = True
    max_file_size: str = "10MB"
    backup_count: int = 5
    structured_logging: bool = True


class SecurityConfig(BaseSettings):
    """Security configuration."""
    api_key_required: bool = False
    api_key_header: str = "X-API-Key"
    allowed_domains: list = []
    max_url_length: int = 2048
    validate_urls: bool = True


class PerformanceConfig(BaseSettings):
    """Performance configuration."""
    download_chunk_size: int = 8192
    max_concurrent_downloads_per_platform: int = 3
    connection_timeout: int = 30
    read_timeout: int = 300
    max_redirects: int = 10


class MonitoringConfig(BaseSettings):
    """Monitoring configuration."""
    enabled: bool = True
    metrics_endpoint: str = "/metrics"
    health_endpoint: str = "/health"
    stats_retention_days: int = 30


class DevelopmentConfig(BaseSettings):
    """Development configuration."""
    debug: bool = False
    reload: bool = False
    profiling: bool = False
    mock_downloads: bool = False


class Config:
    """Main configuration class that loads and manages all settings."""
    
    def __init__(self, config_file: Optional[str] = None):
        """Initialize configuration from file or environment."""
        self.config_file = config_file or self._find_config_file()
        self._config_data = self._load_config()
        
        # Initialize all configuration sections
        self.service = ServiceConfig(**self._config_data.get("service", {}))
        self.api = APIConfig(**self._config_data.get("api", {}))
        self.storage = StorageConfig(**self._config_data.get("storage", {}))
        self.database = DatabaseConfig(**self._config_data.get("database", {}))
        self.queue = QueueConfig(**self._config_data.get("queue", {}))
        self.processing = ProcessingConfig(**self._config_data.get("processing", {}))
        self.logging = LoggingConfig(**self._config_data.get("logging", {}))
        self.security = SecurityConfig(**self._config_data.get("security", {}))
        self.performance = PerformanceConfig(**self._config_data.get("performance", {}))
        self.monitoring = MonitoringConfig(**self._config_data.get("monitoring", {}))
        self.development = DevelopmentConfig(**self._config_data.get("development", {}))
        
        # Platform configurations
        platforms_data = self._config_data.get("platforms", {})
        self.platforms = {
            "youtube": PlatformConfig(**platforms_data.get("youtube", {})),
            "tiktok": PlatformConfig(**platforms_data.get("tiktok", {})),
            "facebook": PlatformConfig(**platforms_data.get("facebook", {})),
        }
        
        # Ensure directories exist
        self._create_directories()
    
    def _find_config_file(self) -> str:
        """Find the configuration file."""
        possible_paths = [
            "config/settings.yaml",
            "settings.yaml",
            os.path.expanduser("~/.video_downloader/settings.yaml"),
            "/etc/video_downloader/settings.yaml",
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        # Return default path if none found
        return "config/settings.yaml"
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file."""
        if not os.path.exists(self.config_file):
            return {}
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f) or {}
        except Exception as e:
            print(f"Warning: Could not load config file {self.config_file}: {e}")
            return {}
    
    def _create_directories(self):
        """Create necessary directories."""
        directories = [
            self.storage.download_dir,
            self.storage.temp_dir,
            self.storage.logs_dir,
        ]
        
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
    
    def get_platform_config(self, platform: str) -> Optional[PlatformConfig]:
        """Get configuration for a specific platform."""
        return self.platforms.get(platform.lower())
    
    def is_platform_enabled(self, platform: str) -> bool:
        """Check if a platform is enabled."""
        config = self.get_platform_config(platform)
        return config.enabled if config else False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary."""
        return {
            "service": self.service.model_dump(),
            "api": self.api.model_dump(),
            "storage": self.storage.model_dump(),
            "database": self.database.model_dump(),
            "queue": self.queue.model_dump(),
            "processing": self.processing.model_dump(),
            "logging": self.logging.model_dump(),
            "security": self.security.model_dump(),
            "performance": self.performance.model_dump(),
            "monitoring": self.monitoring.model_dump(),
            "development": self.development.model_dump(),
            "platforms": {
                name: config.model_dump() 
                for name, config in self.platforms.items()
            },
        }


# Global configuration instance
_config_instance: Optional[Config] = None


def get_config(config_file: Optional[str] = None) -> Config:
    """Get the global configuration instance."""
    global _config_instance
    if _config_instance is None or config_file:
        _config_instance = Config(config_file)
    return _config_instance


def reload_config(config_file: Optional[str] = None):
    """Reload the configuration."""
    global _config_instance
    _config_instance = Config(config_file)

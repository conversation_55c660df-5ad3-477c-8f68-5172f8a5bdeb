"""
Admin API routes for service management.
"""

import logging
from typing import Dict, Any
from fastapi import APIRouter, HTTPException, Depends
from datetime import datetime

from ..models import ServiceConfigResponse, PlatformConfigResponse
from ...queue.job_queue import get_job_queue_manager
from ...downloaders.factory import get_downloader_factory
from ...utils.config import get_config


logger = logging.getLogger(__name__)
router = APIRouter()


# Simple admin authentication (in production, use proper auth)
def admin_required():
    """Simple admin authentication placeholder."""
    # TODO: Implement proper admin authentication
    return True


@router.get("/config", response_model=ServiceConfigResponse)
async def get_service_config(admin: bool = Depends(admin_required)):
    """Get current service configuration."""
    try:
        config = get_config()
        factory = get_downloader_factory()
        
        # Get platform configurations
        supported_platforms = []
        for platform in factory.get_supported_platforms():
            capabilities = factory.get_platform_capabilities(platform)
            platform_config = PlatformConfigResponse(
                platform=platform,
                enabled=capabilities.get("enabled", False),
                max_quality=capabilities.get("max_quality", "1080p"),
                supported_audio_formats=capabilities.get("audio_formats", []),
                supported_video_formats=capabilities.get("video_formats", []),
                features=capabilities.get("features", {})
            )
            supported_platforms.append(platform_config)
        
        return ServiceConfigResponse(
            version=config.service.version,
            max_concurrent_downloads=config.service.max_concurrent_downloads,
            supported_platforms=supported_platforms,
            max_file_size=config.storage.max_file_size,
            rate_limits={"api": config.api.rate_limit}
        )
        
    except Exception as e:
        logger.error(f"Failed to get service config: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get service config: {str(e)}"
        )


@router.post("/queue/clear")
async def clear_queue(admin: bool = Depends(admin_required)):
    """Clear the download queue (admin only)."""
    try:
        queue_manager = get_job_queue_manager()
        
        # Get all pending jobs and cancel them
        # This is a simplified implementation
        stats = await queue_manager.get_queue_stats()
        queue_size = stats.get("queue_size", 0)
        
        # TODO: Implement actual queue clearing
        # For now, just return the current state
        
        logger.warning("Queue clear requested by admin")
        
        return {
            "message": f"Queue clearing initiated",
            "jobs_affected": queue_size,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to clear queue: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to clear queue: {str(e)}"
        )


@router.post("/jobs/{job_id}/priority")
async def set_job_priority(
    job_id: str, 
    priority: int,
    admin: bool = Depends(admin_required)
):
    """Set job priority (admin only)."""
    try:
        queue_manager = get_job_queue_manager()
        job = await queue_manager.get_job_status(job_id)
        
        if not job:
            raise HTTPException(
                status_code=404,
                detail=f"Job {job_id} not found"
            )
        
        # TODO: Implement priority setting
        # This would require modifying the job queue implementation
        
        logger.info(f"Job {job_id} priority set to {priority} by admin")
        
        return {
            "message": f"Job {job_id} priority set to {priority}",
            "job_id": job_id,
            "new_priority": priority
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to set job priority: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to set job priority: {str(e)}"
        )


@router.get("/stats/detailed")
async def get_detailed_stats(admin: bool = Depends(admin_required)):
    """Get detailed service statistics (admin only)."""
    try:
        queue_manager = get_job_queue_manager()
        config = get_config()
        
        # Get queue statistics
        queue_stats = await queue_manager.get_queue_stats()
        
        # Get system information
        import psutil
        import os
        
        # Memory usage
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        
        # Disk usage for download directory
        download_dir_usage = psutil.disk_usage(config.storage.download_dir)
        temp_dir_usage = psutil.disk_usage(config.storage.temp_dir)
        
        # Get recent jobs for analysis
        recent_jobs = await queue_manager.queue.get_completed_jobs(limit=100)
        
        # Calculate success rate
        if recent_jobs:
            successful = len([job for job in recent_jobs if job.status.value == "completed"])
            success_rate = (successful / len(recent_jobs)) * 100
        else:
            success_rate = 0.0
        
        detailed_stats = {
            "service": {
                "version": config.service.version,
                "uptime_seconds": psutil.boot_time(),
                "pid": os.getpid(),
                "workers": config.service.max_concurrent_downloads
            },
            "queue": {
                "pending_jobs": queue_stats.get("queue_size", 0),
                "active_jobs": queue_stats.get("active_jobs", 0),
                "active_job_ids": queue_stats.get("active_job_ids", [])
            },
            "performance": {
                "success_rate_percent": success_rate,
                "total_processed": len(recent_jobs),
                "memory_usage_mb": memory_info.rss / 1024 / 1024,
                "cpu_percent": psutil.cpu_percent()
            },
            "storage": {
                "download_dir": {
                    "path": config.storage.download_dir,
                    "total_gb": download_dir_usage.total / 1024**3,
                    "used_gb": download_dir_usage.used / 1024**3,
                    "free_gb": download_dir_usage.free / 1024**3,
                    "usage_percent": (download_dir_usage.used / download_dir_usage.total) * 100
                },
                "temp_dir": {
                    "path": config.storage.temp_dir,
                    "total_gb": temp_dir_usage.total / 1024**3,
                    "used_gb": temp_dir_usage.used / 1024**3,
                    "free_gb": temp_dir_usage.free / 1024**3,
                    "usage_percent": (temp_dir_usage.used / temp_dir_usage.total) * 100
                }
            },
            "configuration": {
                "platforms_enabled": [
                    platform for platform in ["youtube", "tiktok", "facebook"]
                    if config.is_platform_enabled(platform)
                ],
                "max_file_size": config.storage.max_file_size,
                "cleanup_enabled": config.storage.cleanup_temp_files,
                "cpp_engine_enabled": config.processing.use_cpp_engine
            }
        }
        
        return detailed_stats
        
    except Exception as e:
        logger.error(f"Failed to get detailed stats: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get detailed stats: {str(e)}"
        )


@router.post("/maintenance/cleanup")
async def run_cleanup(admin: bool = Depends(admin_required)):
    """Run maintenance cleanup tasks (admin only)."""
    try:
        queue_manager = get_job_queue_manager()
        config = get_config()
        
        cleanup_results = {
            "old_jobs_cleaned": 0,
            "temp_files_cleaned": 0,
            "disk_space_freed_mb": 0
        }
        
        # Cleanup old jobs
        if hasattr(queue_manager.queue, 'cleanup_old_jobs'):
            old_jobs_cleaned = await queue_manager.queue.cleanup_old_jobs(
                max_age_days=config.monitoring.stats_retention_days
            )
            cleanup_results["old_jobs_cleaned"] = old_jobs_cleaned
        
        # Cleanup temp files
        import os
        import time
        from pathlib import Path
        
        temp_dir = Path(config.storage.temp_dir)
        if temp_dir.exists():
            cutoff_time = time.time() - 3600  # 1 hour ago
            temp_files_cleaned = 0
            disk_space_freed = 0
            
            for file_path in temp_dir.rglob("*"):
                if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                    try:
                        file_size = file_path.stat().st_size
                        file_path.unlink()
                        temp_files_cleaned += 1
                        disk_space_freed += file_size
                    except Exception as e:
                        logger.warning(f"Failed to delete temp file {file_path}: {e}")
            
            cleanup_results["temp_files_cleaned"] = temp_files_cleaned
            cleanup_results["disk_space_freed_mb"] = disk_space_freed / 1024 / 1024
        
        logger.info(f"Maintenance cleanup completed: {cleanup_results}")
        
        return {
            "message": "Maintenance cleanup completed",
            "results": cleanup_results,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to run cleanup: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to run cleanup: {str(e)}"
        )


@router.get("/logs/recent")
async def get_recent_logs(
    lines: int = 100,
    level: str = "INFO",
    admin: bool = Depends(admin_required)
):
    """Get recent log entries (admin only)."""
    try:
        config = get_config()
        log_file = Path(config.storage.logs_dir) / "service.log"
        
        if not log_file.exists():
            return {
                "logs": [],
                "message": "Log file not found"
            }
        
        # Read last N lines from log file
        with open(log_file, 'r') as f:
            all_lines = f.readlines()
            recent_lines = all_lines[-lines:] if len(all_lines) > lines else all_lines
        
        # Filter by log level if specified
        if level != "ALL":
            filtered_lines = [
                line for line in recent_lines 
                if level.upper() in line
            ]
        else:
            filtered_lines = recent_lines
        
        return {
            "logs": [line.strip() for line in filtered_lines],
            "total_lines": len(filtered_lines),
            "log_file": str(log_file)
        }
        
    except Exception as e:
        logger.error(f"Failed to get recent logs: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get recent logs: {str(e)}"
        )

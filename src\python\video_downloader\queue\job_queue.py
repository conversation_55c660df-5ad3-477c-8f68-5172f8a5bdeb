"""
Job queue management for video download tasks.
"""

import asyncio
import json
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import redis
import logging

from ..api.models import JobStatus, DownloadRequest, DownloadProgress
from ..utils.config import get_config


logger = logging.getLogger(__name__)


@dataclass
class Job:
    """Represents a download job."""
    id: str
    request: DownloadRequest
    status: JobStatus
    created_at: datetime
    updated_at: datetime
    priority: int = 0
    retry_count: int = 0
    max_retries: int = 3
    error_message: Optional[str] = None
    progress: Optional[DownloadProgress] = None
    result: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert job to dictionary."""
        data = asdict(self)
        # Convert datetime objects to ISO strings
        data['created_at'] = self.created_at.isoformat()
        data['updated_at'] = self.updated_at.isoformat()
        # Convert request to dict
        data['request'] = self.request.model_dump()
        # Convert progress to dict if exists
        if self.progress:
            data['progress'] = self.progress.model_dump()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Job':
        """Create job from dictionary."""
        # Convert ISO strings back to datetime
        data['created_at'] = datetime.fromisoformat(data['created_at'])
        data['updated_at'] = datetime.fromisoformat(data['updated_at'])
        # Convert request dict back to DownloadRequest
        data['request'] = DownloadRequest(**data['request'])
        # Convert progress dict back to DownloadProgress if exists
        if data.get('progress'):
            data['progress'] = DownloadProgress(**data['progress'])
        return cls(**data)


class QueueBackend(Enum):
    """Queue backend types."""
    REDIS = "redis"
    MEMORY = "memory"
    DATABASE = "database"


class JobQueue:
    """Base job queue interface."""
    
    async def enqueue(self, job: Job) -> str:
        """Add a job to the queue."""
        raise NotImplementedError
    
    async def dequeue(self) -> Optional[Job]:
        """Get the next job from the queue."""
        raise NotImplementedError
    
    async def get_job(self, job_id: str) -> Optional[Job]:
        """Get a specific job by ID."""
        raise NotImplementedError
    
    async def update_job(self, job: Job) -> bool:
        """Update a job's status and progress."""
        raise NotImplementedError
    
    async def delete_job(self, job_id: str) -> bool:
        """Delete a job from the queue."""
        raise NotImplementedError
    
    async def get_queue_size(self) -> int:
        """Get the number of jobs in the queue."""
        raise NotImplementedError
    
    async def get_active_jobs(self) -> List[Job]:
        """Get all active (downloading/processing) jobs."""
        raise NotImplementedError
    
    async def get_completed_jobs(self, limit: int = 100) -> List[Job]:
        """Get completed jobs."""
        raise NotImplementedError
    
    async def cleanup_old_jobs(self, max_age_days: int = 7) -> int:
        """Clean up old completed jobs."""
        raise NotImplementedError


class RedisJobQueue(JobQueue):
    """Redis-based job queue implementation."""
    
    def __init__(self, redis_url: str = "redis://localhost:6379/0"):
        """Initialize Redis job queue."""
        self.redis_url = redis_url
        self.redis_client = redis.from_url(redis_url, decode_responses=True)
        self.queue_key = "video_downloader:queue"
        self.jobs_key = "video_downloader:jobs"
        self.active_key = "video_downloader:active"
        self.completed_key = "video_downloader:completed"
    
    async def enqueue(self, job: Job) -> str:
        """Add a job to the Redis queue."""
        try:
            # Store job data
            job_data = json.dumps(job.to_dict())
            self.redis_client.hset(self.jobs_key, job.id, job_data)
            
            # Add to queue with priority
            self.redis_client.zadd(self.queue_key, {job.id: job.priority})
            
            logger.info(f"Job {job.id} enqueued successfully")
            return job.id
        except Exception as e:
            logger.error(f"Failed to enqueue job {job.id}: {e}")
            raise
    
    async def dequeue(self) -> Optional[Job]:
        """Get the next job from the Redis queue."""
        try:
            # Get highest priority job
            result = self.redis_client.zpopmax(self.queue_key)
            if not result:
                return None
            
            job_id, _ = result[0]
            
            # Get job data
            job_data = self.redis_client.hget(self.jobs_key, job_id)
            if not job_data:
                logger.warning(f"Job {job_id} not found in jobs store")
                return None
            
            job = Job.from_dict(json.loads(job_data))
            
            # Move to active jobs
            self.redis_client.sadd(self.active_key, job_id)
            
            logger.info(f"Job {job_id} dequeued successfully")
            return job
        except Exception as e:
            logger.error(f"Failed to dequeue job: {e}")
            return None
    
    async def get_job(self, job_id: str) -> Optional[Job]:
        """Get a specific job by ID."""
        try:
            job_data = self.redis_client.hget(self.jobs_key, job_id)
            if not job_data:
                return None
            
            return Job.from_dict(json.loads(job_data))
        except Exception as e:
            logger.error(f"Failed to get job {job_id}: {e}")
            return None
    
    async def update_job(self, job: Job) -> bool:
        """Update a job's status and progress."""
        try:
            job.updated_at = datetime.now()
            job_data = json.dumps(job.to_dict())
            self.redis_client.hset(self.jobs_key, job.id, job_data)
            
            # Move to completed if finished
            if job.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
                self.redis_client.srem(self.active_key, job.id)
                self.redis_client.zadd(self.completed_key, {job.id: job.updated_at.timestamp()})
            
            return True
        except Exception as e:
            logger.error(f"Failed to update job {job.id}: {e}")
            return False
    
    async def delete_job(self, job_id: str) -> bool:
        """Delete a job from the queue."""
        try:
            # Remove from all sets and hashes
            self.redis_client.zrem(self.queue_key, job_id)
            self.redis_client.srem(self.active_key, job_id)
            self.redis_client.zrem(self.completed_key, job_id)
            self.redis_client.hdel(self.jobs_key, job_id)
            
            logger.info(f"Job {job_id} deleted successfully")
            return True
        except Exception as e:
            logger.error(f"Failed to delete job {job_id}: {e}")
            return False
    
    async def get_queue_size(self) -> int:
        """Get the number of jobs in the queue."""
        return self.redis_client.zcard(self.queue_key)
    
    async def get_active_jobs(self) -> List[Job]:
        """Get all active jobs."""
        try:
            active_job_ids = self.redis_client.smembers(self.active_key)
            jobs = []
            
            for job_id in active_job_ids:
                job = await self.get_job(job_id)
                if job:
                    jobs.append(job)
            
            return jobs
        except Exception as e:
            logger.error(f"Failed to get active jobs: {e}")
            return []
    
    async def get_completed_jobs(self, limit: int = 100) -> List[Job]:
        """Get completed jobs."""
        try:
            # Get most recent completed jobs
            completed_job_ids = self.redis_client.zrevrange(self.completed_key, 0, limit - 1)
            jobs = []
            
            for job_id in completed_job_ids:
                job = await self.get_job(job_id)
                if job:
                    jobs.append(job)
            
            return jobs
        except Exception as e:
            logger.error(f"Failed to get completed jobs: {e}")
            return []
    
    async def cleanup_old_jobs(self, max_age_days: int = 7) -> int:
        """Clean up old completed jobs."""
        try:
            cutoff_time = datetime.now() - timedelta(days=max_age_days)
            cutoff_timestamp = cutoff_time.timestamp()
            
            # Get old job IDs
            old_job_ids = self.redis_client.zrangebyscore(
                self.completed_key, 0, cutoff_timestamp
            )
            
            if not old_job_ids:
                return 0
            
            # Remove old jobs
            for job_id in old_job_ids:
                await self.delete_job(job_id)
            
            logger.info(f"Cleaned up {len(old_job_ids)} old jobs")
            return len(old_job_ids)
        except Exception as e:
            logger.error(f"Failed to cleanup old jobs: {e}")
            return 0


class MemoryJobQueue(JobQueue):
    """In-memory job queue implementation for testing/development."""
    
    def __init__(self):
        """Initialize memory job queue."""
        self.queue: List[Job] = []
        self.jobs: Dict[str, Job] = {}
        self.active_jobs: Dict[str, Job] = {}
        self.completed_jobs: Dict[str, Job] = {}
        self._lock = asyncio.Lock()
    
    async def enqueue(self, job: Job) -> str:
        """Add a job to the memory queue."""
        async with self._lock:
            self.jobs[job.id] = job
            # Insert job in priority order
            inserted = False
            for i, queued_job in enumerate(self.queue):
                if job.priority > queued_job.priority:
                    self.queue.insert(i, job)
                    inserted = True
                    break
            
            if not inserted:
                self.queue.append(job)
            
            logger.info(f"Job {job.id} enqueued successfully")
            return job.id
    
    async def dequeue(self) -> Optional[Job]:
        """Get the next job from the memory queue."""
        async with self._lock:
            if not self.queue:
                return None
            
            job = self.queue.pop(0)
            self.active_jobs[job.id] = job
            
            logger.info(f"Job {job.id} dequeued successfully")
            return job
    
    async def get_job(self, job_id: str) -> Optional[Job]:
        """Get a specific job by ID."""
        return self.jobs.get(job_id)
    
    async def update_job(self, job: Job) -> bool:
        """Update a job's status and progress."""
        async with self._lock:
            job.updated_at = datetime.now()
            self.jobs[job.id] = job
            
            # Move to completed if finished
            if job.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
                if job.id in self.active_jobs:
                    del self.active_jobs[job.id]
                self.completed_jobs[job.id] = job
            
            return True
    
    async def delete_job(self, job_id: str) -> bool:
        """Delete a job from the queue."""
        async with self._lock:
            # Remove from all collections
            self.queue = [job for job in self.queue if job.id != job_id]
            self.jobs.pop(job_id, None)
            self.active_jobs.pop(job_id, None)
            self.completed_jobs.pop(job_id, None)
            
            logger.info(f"Job {job_id} deleted successfully")
            return True
    
    async def get_queue_size(self) -> int:
        """Get the number of jobs in the queue."""
        return len(self.queue)
    
    async def get_active_jobs(self) -> List[Job]:
        """Get all active jobs."""
        return list(self.active_jobs.values())
    
    async def get_completed_jobs(self, limit: int = 100) -> List[Job]:
        """Get completed jobs."""
        jobs = list(self.completed_jobs.values())
        # Sort by completion time (most recent first)
        jobs.sort(key=lambda x: x.updated_at, reverse=True)
        return jobs[:limit]
    
    async def cleanup_old_jobs(self, max_age_days: int = 7) -> int:
        """Clean up old completed jobs."""
        cutoff_time = datetime.now() - timedelta(days=max_age_days)
        old_job_ids = [
            job_id for job_id, job in self.completed_jobs.items()
            if job.updated_at < cutoff_time
        ]
        
        for job_id in old_job_ids:
            await self.delete_job(job_id)
        
        logger.info(f"Cleaned up {len(old_job_ids)} old jobs")
        return len(old_job_ids)


class JobQueueManager:
    """Job queue manager that handles job lifecycle."""
    
    def __init__(self, queue: JobQueue):
        """Initialize job queue manager."""
        self.queue = queue
        self.config = get_config()
        self.job_handlers: Dict[str, Callable] = {}
    
    def register_handler(self, job_type: str, handler: Callable):
        """Register a job handler."""
        self.job_handlers[job_type] = handler
    
    async def submit_job(self, request: DownloadRequest, priority: int = 0) -> str:
        """Submit a new download job."""
        job_id = str(uuid.uuid4())
        now = datetime.now()
        
        job = Job(
            id=job_id,
            request=request,
            status=JobStatus.PENDING,
            created_at=now,
            updated_at=now,
            priority=priority,
            max_retries=self.config.queue.max_retries
        )
        
        await self.queue.enqueue(job)
        return job_id
    
    async def get_job_status(self, job_id: str) -> Optional[Job]:
        """Get job status."""
        return await self.queue.get_job(job_id)
    
    async def cancel_job(self, job_id: str) -> bool:
        """Cancel a job."""
        job = await self.queue.get_job(job_id)
        if not job:
            return False
        
        if job.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
            return False
        
        job.status = JobStatus.CANCELLED
        job.updated_at = datetime.now()
        
        return await self.queue.update_job(job)
    
    async def retry_job(self, job_id: str) -> bool:
        """Retry a failed job."""
        job = await self.queue.get_job(job_id)
        if not job or job.status != JobStatus.FAILED:
            return False
        
        if job.retry_count >= job.max_retries:
            return False
        
        job.status = JobStatus.PENDING
        job.retry_count += 1
        job.error_message = None
        job.updated_at = datetime.now()
        
        await self.queue.enqueue(job)
        return True
    
    async def get_queue_stats(self) -> Dict[str, Any]:
        """Get queue statistics."""
        queue_size = await self.queue.get_queue_size()
        active_jobs = await self.queue.get_active_jobs()
        
        return {
            "queue_size": queue_size,
            "active_jobs": len(active_jobs),
            "active_job_ids": [job.id for job in active_jobs],
        }


def create_job_queue(backend: str = None) -> JobQueue:
    """Create a job queue instance based on configuration."""
    config = get_config()
    backend = backend or config.queue.backend
    
    if backend == QueueBackend.REDIS.value:
        return RedisJobQueue(config.queue.redis_url)
    elif backend == QueueBackend.MEMORY.value:
        return MemoryJobQueue()
    else:
        raise ValueError(f"Unsupported queue backend: {backend}")


# Global job queue manager instance
_queue_manager: Optional[JobQueueManager] = None


def get_job_queue_manager() -> JobQueueManager:
    """Get the global job queue manager instance."""
    global _queue_manager
    if _queue_manager is None:
        queue = create_job_queue()
        _queue_manager = JobQueueManager(queue)
    return _queue_manager

# Core web framework
fastapi
uvicorn[standard]
pydantic
pydantic-settings

# Video downloading
yt-dlp
requests
aiohttp
aiofiles

# Queue and task management
celery
redis
rq

# Database
sqlalchemy
alembic

# Configuration and utilities
pyyaml
python-dotenv
structlog
rich

# HTTP client and web
httpx
python-multipart
jinja2

# Media processing (simplified)
ffmpeg-python
mutagen

# Development and testing
pytest
pytest-asyncio
click

# Optional: Install Pillow separately if needed
# pillow

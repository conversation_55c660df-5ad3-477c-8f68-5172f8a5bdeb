# Video Downloader Service API Documentation

## Overview

The Video Downloader Service provides a REST API for downloading videos from multiple platforms including YouTube, TikTok, and Facebook. The service supports various quality options, format conversions, and advanced features like watermark removal.

## Base URL

```
http://localhost:8080
```

## Authentication

Currently, the API does not require authentication by default. This can be enabled in the configuration.

## Rate Limiting

The API implements rate limiting to prevent abuse:
- Default: 100 requests per minute per IP
- Rate limit headers are included in responses:
  - `X-RateLimit-Limit`: Maximum requests allowed
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: Time when the rate limit resets

## Endpoints

### Health Check

#### GET /health

Check the service health status.

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2023-12-01T12:00:00Z",
  "version": "1.0.0",
  "uptime": 3600.5,
  "active_downloads": 2,
  "queue_size": 5
}
```

### Download Management

#### POST /api/download

Submit a video download request.

**Request Body:**
```json
{
  "url": "https://www.youtube.com/watch?v=example",
  "quality": "1080p",
  "audio_only": false,
  "audio_format": "mp3",
  "video_format": "mp4",
  "extract_subtitles": false,
  "extract_thumbnails": true,
  "remove_watermark": true,
  "custom_filename": "my_video",
  "output_dir": "/custom/path"
}
```

**Parameters:**
- `url` (required): Video URL from supported platform
- `quality`: Video quality (`360p`, `480p`, `720p`, `1080p`, `1440p`, `4K`, `best`, `worst`)
- `audio_only`: Download audio only (boolean)
- `audio_format`: Audio format (`mp3`, `aac`, `opus`, `m4a`)
- `video_format`: Video format (`mp4`, `webm`, `mkv`)
- `extract_subtitles`: Extract subtitles if available (YouTube only)
- `extract_thumbnails`: Extract video thumbnails
- `remove_watermark`: Remove watermarks (TikTok only)
- `custom_filename`: Custom filename without extension
- `output_dir`: Custom output directory

**Response:**
```json
{
  "job_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "pending",
  "message": "Download request submitted successfully",
  "estimated_time": 30,
  "queue_position": 2
}
```

#### POST /api/download/batch

Submit multiple video download requests.

**Request Body:**
```json
{
  "urls": [
    "https://www.youtube.com/watch?v=example1",
    "https://www.youtube.com/watch?v=example2"
  ],
  "quality": "720p",
  "audio_only": false,
  "audio_format": "mp3",
  "video_format": "mp4"
}
```

**Response:**
```json
{
  "batch_id": "batch-550e8400-e29b-41d4-a716-446655440000",
  "job_ids": [
    "job-1-550e8400-e29b-41d4-a716-446655440000",
    "job-2-550e8400-e29b-41d4-a716-446655440000"
  ],
  "total_jobs": 2,
  "message": "Batch download submitted: 2 jobs created",
  "estimated_total_time": 60
}
```

#### DELETE /api/download/{job_id}

Cancel a download job.

**Response:**
```json
{
  "message": "Job 550e8400-e29b-41d4-a716-446655440000 cancelled successfully"
}
```

#### POST /api/download/{job_id}/retry

Retry a failed download job.

**Response:**
```json
{
  "message": "Job 550e8400-e29b-41d4-a716-446655440000 scheduled for retry"
}
```

### Status and Monitoring

#### GET /api/status/{job_id}

Get the status of a specific download job.

**Response:**
```json
{
  "job_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "downloading",
  "progress": {
    "job_id": "550e8400-e29b-41d4-a716-446655440000",
    "status": "downloading",
    "progress_percent": 45.5,
    "downloaded_bytes": 1048576,
    "total_bytes": 2097152,
    "download_speed": 102400,
    "eta": 10,
    "current_stage": "downloading",
    "created_at": "2023-12-01T12:00:00Z",
    "updated_at": "2023-12-01T12:00:30Z"
  },
  "result": null
}
```

#### GET /api/downloads

List completed downloads with pagination.

**Query Parameters:**
- `page`: Page number (default: 1)
- `page_size`: Items per page (default: 50, max: 100)
- `status`: Filter by status (`pending`, `downloading`, `processing`, `completed`, `failed`, `cancelled`)

**Response:**
```json
{
  "downloads": [
    {
      "job_id": "550e8400-e29b-41d4-a716-446655440000",
      "status": "completed",
      "metadata": {
        "title": "Example Video",
        "duration": 120,
        "uploader": "Example Channel",
        "platform": "youtube"
      },
      "file_path": "/downloads/youtube/2023-12-01/example_video.mp4",
      "file_size": 10485760,
      "processing_time": 25.5,
      "created_at": "2023-12-01T12:00:00Z",
      "completed_at": "2023-12-01T12:00:25Z"
    }
  ],
  "total_count": 100,
  "page": 1,
  "page_size": 50,
  "has_next": true,
  "has_previous": false
}
```

#### GET /api/jobs/active

Get currently active download jobs.

**Response:**
```json
{
  "active_jobs": [
    {
      "job_id": "550e8400-e29b-41d4-a716-446655440000",
      "status": "downloading",
      "url": "https://www.youtube.com/watch?v=example",
      "created_at": "2023-12-01T12:00:00Z",
      "progress": {
        "progress_percent": 45.5,
        "current_stage": "downloading"
      }
    }
  ],
  "count": 1
}
```

#### GET /api/jobs/recent

Get recent download jobs.

**Query Parameters:**
- `limit`: Number of jobs to return (default: 10, max: 100)

**Response:**
```json
{
  "recent_jobs": [
    {
      "job_id": "550e8400-e29b-41d4-a716-446655440000",
      "status": "completed",
      "url": "https://www.youtube.com/watch?v=example",
      "created_at": "2023-12-01T12:00:00Z",
      "updated_at": "2023-12-01T12:00:25Z",
      "result": {
        "file_path": "/downloads/example_video.mp4",
        "file_size": 10485760,
        "processing_time": 25.5
      }
    }
  ],
  "count": 1
}
```

### Platform Information

#### GET /api/download/platforms

Get supported platforms and their capabilities.

**Response:**
```json
{
  "supported_platforms": ["youtube", "tiktok", "facebook"],
  "platform_capabilities": {
    "youtube": {
      "enabled": true,
      "max_quality": "4K",
      "supported_audio_formats": ["mp3", "aac", "opus"],
      "supported_video_formats": ["mp4", "webm", "mkv"],
      "features": {
        "watermark_removal": false,
        "subtitle_extraction": true,
        "live_streams": true
      }
    },
    "tiktok": {
      "enabled": true,
      "max_quality": "1080p",
      "supported_audio_formats": ["mp3", "aac"],
      "supported_video_formats": ["mp4"],
      "features": {
        "watermark_removal": true,
        "subtitle_extraction": false,
        "live_streams": false
      }
    }
  }
}
```

#### GET /api/download/queue/stats

Get download queue statistics.

**Response:**
```json
{
  "queue_size": 5,
  "active_jobs": 2,
  "active_job_ids": [
    "550e8400-e29b-41d4-a716-446655440000",
    "660e8400-e29b-41d4-a716-446655440001"
  ]
}
```

### Metrics

#### GET /api/metrics

Get service metrics and statistics.

**Response:**
```json
{
  "total_downloads": 1000,
  "successful_downloads": 950,
  "failed_downloads": 50,
  "average_download_time": 25.5,
  "platform_stats": {
    "youtube": {
      "total": 600,
      "successful": 580,
      "failed": 20
    },
    "tiktok": {
      "total": 300,
      "successful": 290,
      "failed": 10
    }
  },
  "quality_stats": {
    "1080p": 400,
    "720p": 300,
    "best": 200
  },
  "format_stats": {
    "mp4": 800,
    "mp3": 150,
    "webm": 50
  },
  "timestamp": "2023-12-01T12:00:00Z"
}
```

## Error Responses

All error responses follow this format:

```json
{
  "error": "ValidationError",
  "message": "Invalid video URL",
  "details": {
    "field": "url",
    "value": "invalid-url"
  },
  "timestamp": "2023-12-01T12:00:00Z"
}
```

### HTTP Status Codes

- `200 OK`: Request successful
- `400 Bad Request`: Invalid request data
- `404 Not Found`: Resource not found
- `409 Conflict`: Resource conflict
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error

## WebSocket Support (Future)

Real-time progress updates will be available via WebSocket connections:

```javascript
const ws = new WebSocket('ws://localhost:8080/ws/progress/{job_id}');
ws.onmessage = function(event) {
    const progress = JSON.parse(event.data);
    console.log('Progress:', progress.progress_percent + '%');
};
```

## SDKs and Libraries

Official SDKs are planned for:
- Python
- JavaScript/Node.js
- Go
- Rust

## Interactive Documentation

- **Swagger UI**: `/docs`
- **ReDoc**: `/redoc`
- **Web Interface**: `/web`

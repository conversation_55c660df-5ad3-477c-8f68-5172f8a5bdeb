{% extends "base.html" %}

{% block title %}Jobs - Video Downloader Service{% endblock %}

{% block content %}
<div class="card">
    <h2>Download Jobs</h2>
    
    {% if active_jobs %}
    <h3>Active Jobs ({{ active_jobs|length }})</h3>
    <div style="overflow-x: auto; margin-bottom: 30px;">
        <table style="width: 100%; border-collapse: collapse;">
            <thead>
                <tr style="border-bottom: 2px solid #ecf0f1;">
                    <th style="text-align: left; padding: 10px;">Job ID</th>
                    <th style="text-align: left; padding: 10px;">Status</th>
                    <th style="text-align: left; padding: 10px;">URL</th>
                    <th style="text-align: left; padding: 10px;">Progress</th>
                    <th style="text-align: left; padding: 10px;">Created</th>
                    <th style="text-align: left; padding: 10px;">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for job in active_jobs %}
                <tr style="border-bottom: 1px solid #ecf0f1;">
                    <td style="padding: 10px; font-family: monospace; font-size: 12px;">
                        {{ job.id[:8] }}...
                    </td>
                    <td style="padding: 10px;">
                        <span class="status status-{{ job.status.value }}">
                            {{ job.status.value }}
                        </span>
                    </td>
                    <td style="padding: 10px;">
                        <div style="max-width: 250px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                            {{ job.request.url }}
                        </div>
                    </td>
                    <td style="padding: 10px;">
                        {% if job.progress %}
                        <div class="progress-bar" style="width: 100px; height: 10px;">
                            <div class="progress-fill" style="width: {{ job.progress.progress_percent }}%"></div>
                        </div>
                        <small>{{ "%.1f"|format(job.progress.progress_percent) }}%</small>
                        {% else %}
                        <span style="color: #7f8c8d;">-</span>
                        {% endif %}
                    </td>
                    <td style="padding: 10px; font-size: 12px;">
                        {{ job.created_at.strftime('%m/%d %H:%M') }}
                    </td>
                    <td style="padding: 10px;">
                        <a href="/web/status/{{ job.id }}" class="btn" style="font-size: 12px; padding: 5px 10px;">
                            View
                        </a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% endif %}
    
    {% if recent_jobs %}
    <h3>Recent Jobs ({{ recent_jobs|length }})</h3>
    <div style="overflow-x: auto;">
        <table style="width: 100%; border-collapse: collapse;">
            <thead>
                <tr style="border-bottom: 2px solid #ecf0f1;">
                    <th style="text-align: left; padding: 10px;">Job ID</th>
                    <th style="text-align: left; padding: 10px;">Status</th>
                    <th style="text-align: left; padding: 10px;">URL</th>
                    <th style="text-align: left; padding: 10px;">Quality</th>
                    <th style="text-align: left; padding: 10px;">Created</th>
                    <th style="text-align: left; padding: 10px;">Completed</th>
                    <th style="text-align: left; padding: 10px;">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for job in recent_jobs %}
                <tr style="border-bottom: 1px solid #ecf0f1;">
                    <td style="padding: 10px; font-family: monospace; font-size: 12px;">
                        {{ job.id[:8] }}...
                    </td>
                    <td style="padding: 10px;">
                        <span class="status status-{{ job.status.value }}">
                            {{ job.status.value }}
                        </span>
                    </td>
                    <td style="padding: 10px;">
                        <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                            {{ job.request.url }}
                        </div>
                    </td>
                    <td style="padding: 10px; font-size: 12px;">
                        {% if job.request.audio_only %}
                        Audio ({{ job.request.audio_format.value }})
                        {% else %}
                        {{ job.request.quality.value }} ({{ job.request.video_format.value }})
                        {% endif %}
                    </td>
                    <td style="padding: 10px; font-size: 12px;">
                        {{ job.created_at.strftime('%m/%d %H:%M') }}
                    </td>
                    <td style="padding: 10px; font-size: 12px;">
                        {% if job.status.value == 'completed' %}
                        {{ job.updated_at.strftime('%m/%d %H:%M') }}
                        {% else %}
                        <span style="color: #7f8c8d;">-</span>
                        {% endif %}
                    </td>
                    <td style="padding: 10px;">
                        <a href="/web/status/{{ job.id }}" class="btn" style="font-size: 12px; padding: 5px 10px;">
                            View
                        </a>
                        {% if job.status.value == 'failed' %}
                        <button onclick="retryJob('{{ job.id }}')" class="btn btn-success" 
                                style="font-size: 12px; padding: 5px 10px; margin-left: 5px;">
                            Retry
                        </button>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div class="alert alert-info">
        <p>No recent jobs found. <a href="/web">Start a download</a> to see jobs here.</p>
    </div>
    {% endif %}
</div>

<div class="card">
    <h3>Job Statistics</h3>
    <div class="stats">
        <div class="stat-card">
            <div class="stat-number">
                {{ recent_jobs | selectattr("status.value", "equalto", "completed") | list | length }}
            </div>
            <div class="stat-label">Completed</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">
                {{ recent_jobs | selectattr("status.value", "equalto", "failed") | list | length }}
            </div>
            <div class="stat-label">Failed</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">
                {{ active_jobs | length }}
            </div>
            <div class="stat-label">Active</div>
        </div>
        <div class="stat-card">
            <div class="stat-number">
                {% set completed_jobs = recent_jobs | selectattr("status.value", "equalto", "completed") | list %}
                {% if completed_jobs %}
                {{ "%.1f"|format((completed_jobs | length / recent_jobs | length) * 100) }}%
                {% else %}
                0%
                {% endif %}
            </div>
            <div class="stat-label">Success Rate</div>
        </div>
    </div>
</div>

<div class="card">
    <h3>Platform Breakdown</h3>
    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px;">
        {% set youtube_jobs = recent_jobs | selectattr("request.url", "search", "youtube") | list %}
        {% set tiktok_jobs = recent_jobs | selectattr("request.url", "search", "tiktok") | list %}
        {% set facebook_jobs = recent_jobs | selectattr("request.url", "search", "facebook") | list %}
        
        <div class="stat-card">
            <div class="stat-number" style="color: #ff0000;">{{ youtube_jobs | length }}</div>
            <div class="stat-label">YouTube</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" style="color: #000000;">{{ tiktok_jobs | length }}</div>
            <div class="stat-label">TikTok</div>
        </div>
        <div class="stat-card">
            <div class="stat-number" style="color: #1877f2;">{{ facebook_jobs | length }}</div>
            <div class="stat-label">Facebook</div>
        </div>
    </div>
</div>

<div style="margin-top: 20px;">
    <a href="/web" class="btn">New Download</a>
    <button onclick="refreshJobs()" class="btn">Refresh</button>
    <button onclick="clearCompleted()" class="btn btn-danger">Clear Completed</button>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    function refreshJobs() {
        location.reload();
    }
    
    function retryJob(jobId) {
        if (confirm('Retry this failed job?')) {
            fetch('/api/download/' + jobId + '/retry', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                alert('Job scheduled for retry');
                location.reload();
            })
            .catch(error => {
                alert('Failed to retry job: ' + error);
            });
        }
    }
    
    function clearCompleted() {
        if (confirm('Clear all completed jobs? This action cannot be undone.')) {
            // This would require an admin endpoint
            alert('Clear completed jobs functionality not yet implemented');
        }
    }
    
    // Auto-refresh every 30 seconds
    setInterval(function() {
        // Only refresh if there are active jobs
        const activeJobsCount = {{ active_jobs | length }};
        if (activeJobsCount > 0) {
            location.reload();
        }
    }, 30000);
    
    // Add tooltips for truncated URLs
    document.addEventListener('DOMContentLoaded', function() {
        const urlCells = document.querySelectorAll('td div[style*="text-overflow: ellipsis"]');
        urlCells.forEach(cell => {
            cell.title = cell.textContent;
        });
    });
</script>
{% endblock %}

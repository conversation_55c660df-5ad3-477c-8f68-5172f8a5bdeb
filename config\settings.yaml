# Video Downloader Service Configuration

# Service settings
service:
  name: "video-downloader-service"
  version: "1.0.0"
  host: "0.0.0.0"
  port: 8080
  workers: 4
  max_concurrent_downloads: 10
  daemon_mode: false
  pid_file: "./logs/service.pid"

# API settings
api:
  title: "Video Downloader API"
  description: "Multi-platform video downloader service"
  version: "1.0.0"
  docs_url: "/docs"
  redoc_url: "/redoc"
  cors_enabled: true
  cors_origins: ["*"]
  rate_limit: "100/minute"

# Storage configuration
storage:
  download_dir: "./downloads"
  temp_dir: "./temp"
  logs_dir: "./logs"
  max_file_size: "2GB"
  cleanup_temp_files: true
  cleanup_interval: 3600  # seconds
  organize_by_platform: true
  organize_by_date: true

# Database settings
database:
  url: "sqlite:///./video_downloader.db"
  echo: false
  pool_size: 10
  max_overflow: 20

# Queue settings
queue:
  backend: "redis"  # redis, memory, or database
  redis_url: "redis://localhost:6379/0"
  max_retries: 3
  retry_delay: 60  # seconds
  job_timeout: 3600  # seconds
  result_ttl: 86400  # seconds

# Platform-specific settings
platforms:
  youtube:
    enabled: true
    max_quality: "4K"
    audio_formats: ["mp3", "aac", "opus"]
    video_formats: ["mp4", "webm", "mkv"]
    extract_subtitles: true
    extract_thumbnails: true
    
  tiktok:
    enabled: true
    max_quality: "1080p"
    remove_watermark: true
    audio_formats: ["mp3", "aac"]
    video_formats: ["mp4"]
    extract_thumbnails: true
    
  facebook:
    enabled: true
    max_quality: "1080p"
    audio_formats: ["mp3", "aac"]
    video_formats: ["mp4"]
    extract_thumbnails: false

# Video processing settings
processing:
  use_cpp_engine: true
  ffmpeg_path: "ffmpeg"  # Will auto-detect if not specified
  concurrent_conversions: 2
  temp_cleanup: true
  preserve_metadata: true
  
  # Quality presets
  quality_presets:
    "4K": "2160p"
    "1080p": "1080p"
    "720p": "720p"
    "480p": "480p"
    "360p": "360p"
    "audio_only": "bestaudio"

# Logging configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_logging: true
  console_logging: true
  max_file_size: "10MB"
  backup_count: 5
  structured_logging: true

# Security settings
security:
  api_key_required: false
  api_key_header: "X-API-Key"
  allowed_domains: []  # Empty means all domains allowed
  max_url_length: 2048
  validate_urls: true

# Performance settings
performance:
  download_chunk_size: 8192
  max_concurrent_downloads_per_platform: 3
  connection_timeout: 30
  read_timeout: 300
  max_redirects: 10
  
# Monitoring and metrics
monitoring:
  enabled: true
  metrics_endpoint: "/metrics"
  health_endpoint: "/health"
  stats_retention_days: 30

# Development settings
development:
  debug: false
  reload: false
  profiling: false
  mock_downloads: false

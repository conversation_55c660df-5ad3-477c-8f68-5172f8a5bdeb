#pragma once

#include <string>
#include <vector>
#include <memory>
#include <functional>

extern "C" {
#include <libavcodec/avcodec.h>
#include <libavformat/avformat.h>
#include <libavutil/avutil.h>
#include <libswscale/swscale.h>
#include <libswresample/swresample.h>
}

namespace video_processor {

// Forward declarations
class VideoMetadata;
class ProcessingOptions;

// Progress callback type
using ProgressCallback = std::function<void(double progress, const std::string& stage)>;

// Error handling
class VideoProcessorError : public std::exception {
public:
    explicit VideoProcessorError(const std::string& message) : message_(message) {}
    const char* what() const noexcept override { return message_.c_str(); }

private:
    std::string message_;
};

// Video metadata structure
struct VideoInfo {
    std::string filename;
    std::string format;
    int64_t duration_ms;
    int width;
    int height;
    double fps;
    int64_t bitrate;
    std::string video_codec;
    std::string audio_codec;
    int audio_sample_rate;
    int audio_channels;
    std::vector<std::string> subtitle_languages;
};

// Processing options
struct ConversionOptions {
    std::string output_format = "mp4";
    std::string video_codec = "libx264";
    std::string audio_codec = "aac";
    int video_bitrate = 0;  // 0 = auto
    int audio_bitrate = 192000;  // 192 kbps
    int width = 0;  // 0 = keep original
    int height = 0;  // 0 = keep original
    double fps = 0.0;  // 0 = keep original
    bool audio_only = false;
    bool preserve_metadata = true;
    int quality_preset = 23;  // CRF value for x264
};

struct WatermarkRemovalOptions {
    int crop_left = 0;
    int crop_right = 0;
    int crop_top = 0;
    int crop_bottom = 0;
    bool use_delogo_filter = false;
    std::string delogo_params;
};

// Main video processor class
class VideoProcessor {
public:
    VideoProcessor();
    ~VideoProcessor();

    // Initialize FFmpeg libraries
    bool initialize();
    void cleanup();

    // Metadata extraction
    VideoInfo extract_metadata(const std::string& input_path);

    // Format conversion
    bool convert_video(
        const std::string& input_path,
        const std::string& output_path,
        const ConversionOptions& options,
        ProgressCallback progress_callback = nullptr
    );

    // Audio extraction
    bool extract_audio(
        const std::string& input_path,
        const std::string& output_path,
        const std::string& audio_format = "mp3",
        int bitrate = 192000,
        ProgressCallback progress_callback = nullptr
    );

    // Watermark removal
    bool remove_watermark(
        const std::string& input_path,
        const std::string& output_path,
        const WatermarkRemovalOptions& options,
        ProgressCallback progress_callback = nullptr
    );

    // Video quality adjustment
    bool adjust_quality(
        const std::string& input_path,
        const std::string& output_path,
        int target_height,
        int quality_preset = 23,
        ProgressCallback progress_callback = nullptr
    );

    // Thumbnail generation
    bool generate_thumbnail(
        const std::string& input_path,
        const std::string& output_path,
        int64_t timestamp_ms = 0,
        int width = 320,
        int height = 240
    );

    // Subtitle extraction
    std::vector<std::string> extract_subtitles(
        const std::string& input_path,
        const std::string& output_dir
    );

    // Utility functions
    static std::string get_ffmpeg_version();
    static std::vector<std::string> get_supported_formats();
    static std::vector<std::string> get_supported_codecs();

private:
    bool initialized_;
    
    // Internal helper methods
    AVFormatContext* open_input_file(const std::string& filename);
    AVFormatContext* create_output_context(const std::string& filename, const std::string& format);
    AVCodecContext* create_encoder_context(const AVCodec* codec, const ConversionOptions& options);
    AVCodecContext* create_decoder_context(AVStream* stream);
    
    bool setup_video_encoder(AVCodecContext* enc_ctx, const ConversionOptions& options);
    bool setup_audio_encoder(AVCodecContext* enc_ctx, const ConversionOptions& options);
    
    bool process_video_stream(
        AVFormatContext* input_ctx,
        AVFormatContext* output_ctx,
        int video_stream_index,
        const ConversionOptions& options,
        ProgressCallback progress_callback
    );
    
    bool process_audio_stream(
        AVFormatContext* input_ctx,
        AVFormatContext* output_ctx,
        int audio_stream_index,
        const ConversionOptions& options,
        ProgressCallback progress_callback
    );

    // Filtering
    bool apply_video_filters(
        AVFrame* input_frame,
        AVFrame* output_frame,
        const ConversionOptions& options
    );

    bool apply_watermark_removal_filter(
        AVFrame* input_frame,
        AVFrame* output_frame,
        const WatermarkRemovalOptions& options
    );

    // Progress tracking
    void update_progress(
        int64_t current_pts,
        int64_t total_duration,
        const std::string& stage,
        ProgressCallback callback
    );

    // Error handling
    std::string get_av_error_string(int error_code);
    void log_error(const std::string& message, int av_error = 0);
};

// Utility functions
namespace utils {
    std::string format_duration(int64_t duration_ms);
    std::string format_bitrate(int64_t bitrate);
    std::string format_file_size(int64_t size_bytes);
    
    bool is_video_format(const std::string& format);
    bool is_audio_format(const std::string& format);
    
    std::string get_file_extension(const std::string& format);
    std::string detect_format_from_extension(const std::string& filename);
}

} // namespace video_processor

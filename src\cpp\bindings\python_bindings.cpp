#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
#include <pybind11/functional.h>
#include "../video_processor/video_processor.h"

namespace py = pybind11;
using namespace video_processor;

PYBIND11_MODULE(video_processor_cpp, m) {
    m.doc() = "High-performance C++ video processing module for the video downloader service";

    // Exception binding
    py::register_exception<VideoProcessorError>(m, "VideoProcessorError");

    // VideoInfo structure
    py::class_<VideoInfo>(m, "VideoInfo")
        .def(py::init<>())
        .def_readwrite("filename", &VideoInfo::filename)
        .def_readwrite("format", &VideoInfo::format)
        .def_readwrite("duration_ms", &VideoInfo::duration_ms)
        .def_readwrite("width", &VideoInfo::width)
        .def_readwrite("height", &VideoInfo::height)
        .def_readwrite("fps", &VideoInfo::fps)
        .def_readwrite("bitrate", &VideoInfo::bitrate)
        .def_readwrite("video_codec", &VideoInfo::video_codec)
        .def_readwrite("audio_codec", &VideoInfo::audio_codec)
        .def_readwrite("audio_sample_rate", &VideoInfo::audio_sample_rate)
        .def_readwrite("audio_channels", &VideoInfo::audio_channels)
        .def_readwrite("subtitle_languages", &VideoInfo::subtitle_languages)
        .def("__repr__", [](const VideoInfo& info) {
            return "<VideoInfo: " + info.filename + " (" + 
                   std::to_string(info.width) + "x" + std::to_string(info.height) + ")>";
        });

    // ConversionOptions structure
    py::class_<ConversionOptions>(m, "ConversionOptions")
        .def(py::init<>())
        .def_readwrite("output_format", &ConversionOptions::output_format)
        .def_readwrite("video_codec", &ConversionOptions::video_codec)
        .def_readwrite("audio_codec", &ConversionOptions::audio_codec)
        .def_readwrite("video_bitrate", &ConversionOptions::video_bitrate)
        .def_readwrite("audio_bitrate", &ConversionOptions::audio_bitrate)
        .def_readwrite("width", &ConversionOptions::width)
        .def_readwrite("height", &ConversionOptions::height)
        .def_readwrite("fps", &ConversionOptions::fps)
        .def_readwrite("audio_only", &ConversionOptions::audio_only)
        .def_readwrite("preserve_metadata", &ConversionOptions::preserve_metadata)
        .def_readwrite("quality_preset", &ConversionOptions::quality_preset);

    // WatermarkRemovalOptions structure
    py::class_<WatermarkRemovalOptions>(m, "WatermarkRemovalOptions")
        .def(py::init<>())
        .def_readwrite("crop_left", &WatermarkRemovalOptions::crop_left)
        .def_readwrite("crop_right", &WatermarkRemovalOptions::crop_right)
        .def_readwrite("crop_top", &WatermarkRemovalOptions::crop_top)
        .def_readwrite("crop_bottom", &WatermarkRemovalOptions::crop_bottom)
        .def_readwrite("use_delogo_filter", &WatermarkRemovalOptions::use_delogo_filter)
        .def_readwrite("delogo_params", &WatermarkRemovalOptions::delogo_params);

    // VideoProcessor class
    py::class_<VideoProcessor>(m, "VideoProcessor")
        .def(py::init<>())
        .def("initialize", &VideoProcessor::initialize,
             "Initialize FFmpeg libraries")
        .def("cleanup", &VideoProcessor::cleanup,
             "Cleanup FFmpeg resources")
        .def("extract_metadata", &VideoProcessor::extract_metadata,
             "Extract video metadata from file",
             py::arg("input_path"))
        .def("convert_video", &VideoProcessor::convert_video,
             "Convert video with specified options",
             py::arg("input_path"), py::arg("output_path"), py::arg("options"),
             py::arg("progress_callback") = nullptr)
        .def("extract_audio", &VideoProcessor::extract_audio,
             "Extract audio from video file",
             py::arg("input_path"), py::arg("output_path"),
             py::arg("audio_format") = "mp3", py::arg("bitrate") = 192000,
             py::arg("progress_callback") = nullptr)
        .def("remove_watermark", &VideoProcessor::remove_watermark,
             "Remove watermark from video",
             py::arg("input_path"), py::arg("output_path"), py::arg("options"),
             py::arg("progress_callback") = nullptr)
        .def("adjust_quality", &VideoProcessor::adjust_quality,
             "Adjust video quality",
             py::arg("input_path"), py::arg("output_path"), py::arg("target_height"),
             py::arg("quality_preset") = 23, py::arg("progress_callback") = nullptr)
        .def("generate_thumbnail", &VideoProcessor::generate_thumbnail,
             "Generate video thumbnail",
             py::arg("input_path"), py::arg("output_path"),
             py::arg("timestamp_ms") = 0, py::arg("width") = 320, py::arg("height") = 240)
        .def("extract_subtitles", &VideoProcessor::extract_subtitles,
             "Extract subtitles from video",
             py::arg("input_path"), py::arg("output_dir"))
        .def_static("get_ffmpeg_version", &VideoProcessor::get_ffmpeg_version,
                   "Get FFmpeg version string")
        .def_static("get_supported_formats", &VideoProcessor::get_supported_formats,
                   "Get list of supported formats")
        .def_static("get_supported_codecs", &VideoProcessor::get_supported_codecs,
                   "Get list of supported codecs");

    // Utility functions
    py::module utils = m.def_submodule("utils", "Utility functions");
    
    utils.def("format_duration", &utils::format_duration,
              "Format duration in milliseconds to HH:MM:SS",
              py::arg("duration_ms"));
    
    utils.def("format_bitrate", &utils::format_bitrate,
              "Format bitrate to human readable string",
              py::arg("bitrate"));
    
    utils.def("format_file_size", &utils::format_file_size,
              "Format file size to human readable string",
              py::arg("size_bytes"));
    
    utils.def("is_video_format", &utils::is_video_format,
              "Check if format is a video format",
              py::arg("format"));
    
    utils.def("is_audio_format", &utils::is_audio_format,
              "Check if format is an audio format",
              py::arg("format"));
    
    utils.def("get_file_extension", &utils::get_file_extension,
              "Get file extension for format",
              py::arg("format"));
    
    utils.def("detect_format_from_extension", &utils::detect_format_from_extension,
              "Detect format from file extension",
              py::arg("filename"));

    // Convenience functions for common operations
    m.def("extract_metadata", [](const std::string& input_path) {
        VideoProcessor processor;
        processor.initialize();
        auto result = processor.extract_metadata(input_path);
        processor.cleanup();
        return result;
    }, "Extract metadata from video file", py::arg("input_path"));

    m.def("convert_to_mp4", [](const std::string& input_path, const std::string& output_path,
                              int target_height = 0, int quality = 23,
                              py::function progress_callback = py::none()) {
        VideoProcessor processor;
        processor.initialize();
        
        ConversionOptions options;
        options.output_format = "mp4";
        options.video_codec = "libx264";
        options.audio_codec = "aac";
        options.quality_preset = quality;
        if (target_height > 0) {
            options.height = target_height;
        }
        
        ProgressCallback cpp_callback = nullptr;
        if (!progress_callback.is_none()) {
            cpp_callback = [progress_callback](double progress, const std::string& stage) {
                progress_callback(progress, stage);
            };
        }
        
        bool result = processor.convert_video(input_path, output_path, options, cpp_callback);
        processor.cleanup();
        return result;
    }, "Convert video to MP4 format",
       py::arg("input_path"), py::arg("output_path"),
       py::arg("target_height") = 0, py::arg("quality") = 23,
       py::arg("progress_callback") = py::none());

    m.def("extract_audio_mp3", [](const std::string& input_path, const std::string& output_path,
                                 int bitrate = 192000, py::function progress_callback = py::none()) {
        VideoProcessor processor;
        processor.initialize();
        
        ProgressCallback cpp_callback = nullptr;
        if (!progress_callback.is_none()) {
            cpp_callback = [progress_callback](double progress, const std::string& stage) {
                progress_callback(progress, stage);
            };
        }
        
        bool result = processor.extract_audio(input_path, output_path, "mp3", bitrate, cpp_callback);
        processor.cleanup();
        return result;
    }, "Extract audio as MP3",
       py::arg("input_path"), py::arg("output_path"),
       py::arg("bitrate") = 192000, py::arg("progress_callback") = py::none());

    m.def("remove_watermark", [](const std::string& input_path, const std::string& output_path,
                                int crop_right = 0, int crop_bottom = 0,
                                py::function progress_callback = py::none()) {
        VideoProcessor processor;
        processor.initialize();
        
        WatermarkRemovalOptions options;
        options.crop_right = crop_right;
        options.crop_bottom = crop_bottom;
        
        ProgressCallback cpp_callback = nullptr;
        if (!progress_callback.is_none()) {
            cpp_callback = [progress_callback](double progress, const std::string& stage) {
                progress_callback(progress, stage);
            };
        }
        
        bool result = processor.remove_watermark(input_path, output_path, options, cpp_callback);
        processor.cleanup();
        return result;
    }, "Remove watermark from video",
       py::arg("input_path"), py::arg("output_path"),
       py::arg("crop_right") = 0, py::arg("crop_bottom") = 0,
       py::arg("progress_callback") = py::none());

    m.def("generate_thumbnail", [](const std::string& input_path, const std::string& output_path,
                                  int timestamp_ms = 0, int width = 320, int height = 240) {
        VideoProcessor processor;
        processor.initialize();
        bool result = processor.generate_thumbnail(input_path, output_path, timestamp_ms, width, height);
        processor.cleanup();
        return result;
    }, "Generate video thumbnail",
       py::arg("input_path"), py::arg("output_path"),
       py::arg("timestamp_ms") = 0, py::arg("width") = 320, py::arg("height") = 240);

    // Version information
    m.attr("__version__") = "1.0.0";
    m.attr("__author__") = "Video Downloader Team";
}

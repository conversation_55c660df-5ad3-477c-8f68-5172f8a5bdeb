"""
Status and monitoring API routes.
"""

import logging
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Query
from datetime import datetime

from ..models import (
    JobStatusResponse, DownloadListResponse, HealthResponse, 
    MetricsResponse, JobStatus
)
from ...queue.job_queue import get_job_queue_manager
from ...utils.config import get_config


logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/status/{job_id}", response_model=JobStatusResponse)
async def get_job_status(job_id: str):
    """Get status of a specific download job."""
    try:
        queue_manager = get_job_queue_manager()
        job = await queue_manager.get_job_status(job_id)
        
        if not job:
            raise HTTPException(
                status_code=404,
                detail=f"Job {job_id} not found"
            )
        
        # Create response
        response = JobStatusResponse(
            job_id=job.id,
            status=job.status,
            progress=job.progress if job.progress else None,
            result=job.result if job.status == JobStatus.COMPLETED else None
        )
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get job status for {job_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get job status: {str(e)}"
        )


@router.get("/downloads", response_model=DownloadListResponse)
async def list_downloads(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(50, ge=1, le=100, description="Items per page"),
    status: Optional[JobStatus] = Query(None, description="Filter by status")
):
    """List download jobs with pagination."""
    try:
        queue_manager = get_job_queue_manager()
        
        # Get completed jobs (for now, we'll implement filtering later)
        jobs = await queue_manager.queue.get_completed_jobs(limit=page_size * 10)
        
        # Filter by status if specified
        if status:
            jobs = [job for job in jobs if job.status == status]
        
        # Calculate pagination
        total_count = len(jobs)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        page_jobs = jobs[start_idx:end_idx]
        
        # Convert jobs to download results
        downloads = []
        for job in page_jobs:
            if job.result:
                downloads.append(job.result)
        
        has_next = end_idx < total_count
        has_previous = page > 1
        
        return DownloadListResponse(
            downloads=downloads,
            total_count=total_count,
            page=page,
            page_size=page_size,
            has_next=has_next,
            has_previous=has_previous
        )
        
    except Exception as e:
        logger.error(f"Failed to list downloads: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to list downloads: {str(e)}"
        )


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint with system information."""
    try:
        config = get_config()
        queue_manager = get_job_queue_manager()
        
        # Get queue statistics
        stats = await queue_manager.get_queue_stats()
        
        # Get system information
        import psutil
        import time
        
        system_info = {
            "cpu_percent": psutil.cpu_percent(),
            "memory_percent": psutil.virtual_memory().percent,
            "disk_usage": psutil.disk_usage('/').percent,
            "load_average": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None,
        }
        
        # Calculate uptime (simplified)
        uptime = time.time() - psutil.boot_time()
        
        return HealthResponse(
            status="healthy",
            timestamp=datetime.now(),
            version=config.service.version,
            uptime=uptime,
            active_downloads=stats.get("active_jobs", 0),
            queue_size=stats.get("queue_size", 0),
            system_info=system_info
        )
        
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        # Return unhealthy status but don't raise exception
        return HealthResponse(
            status="unhealthy",
            timestamp=datetime.now(),
            version="unknown",
            uptime=0,
            active_downloads=0,
            queue_size=0,
            system_info={"error": str(e)}
        )


@router.get("/metrics", response_model=MetricsResponse)
async def get_metrics():
    """Get service metrics and statistics."""
    try:
        queue_manager = get_job_queue_manager()
        
        # Get completed jobs for statistics
        completed_jobs = await queue_manager.queue.get_completed_jobs(limit=1000)
        
        # Calculate metrics
        total_downloads = len(completed_jobs)
        successful_downloads = len([job for job in completed_jobs if job.status == JobStatus.COMPLETED])
        failed_downloads = len([job for job in completed_jobs if job.status == JobStatus.FAILED])
        
        # Calculate average download time
        processing_times = []
        for job in completed_jobs:
            if job.result and hasattr(job.result, 'processing_time') and job.result.processing_time:
                processing_times.append(job.result.processing_time)
        
        average_download_time = sum(processing_times) / len(processing_times) if processing_times else 0.0
        
        # Platform statistics
        platform_stats = {}
        for job in completed_jobs:
            if job.result and hasattr(job.result, 'metadata') and job.result.metadata:
                platform = job.result.metadata.platform.value
                if platform not in platform_stats:
                    platform_stats[platform] = {"total": 0, "successful": 0, "failed": 0}
                
                platform_stats[platform]["total"] += 1
                if job.status == JobStatus.COMPLETED:
                    platform_stats[platform]["successful"] += 1
                else:
                    platform_stats[platform]["failed"] += 1
        
        # Quality statistics (simplified)
        quality_stats = {}
        format_stats = {}
        
        # TODO: Implement detailed quality and format statistics
        # This would require storing more detailed job information
        
        return MetricsResponse(
            total_downloads=total_downloads,
            successful_downloads=successful_downloads,
            failed_downloads=failed_downloads,
            average_download_time=average_download_time,
            platform_stats=platform_stats,
            quality_stats=quality_stats,
            format_stats=format_stats,
            timestamp=datetime.now()
        )
        
    except Exception as e:
        logger.error(f"Failed to get metrics: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get metrics: {str(e)}"
        )


@router.get("/jobs/active")
async def get_active_jobs():
    """Get currently active download jobs."""
    try:
        queue_manager = get_job_queue_manager()
        active_jobs = await queue_manager.queue.get_active_jobs()
        
        # Convert to simple format
        jobs_info = []
        for job in active_jobs:
            job_info = {
                "job_id": job.id,
                "status": job.status.value,
                "url": str(job.request.url),
                "created_at": job.created_at.isoformat(),
                "progress": job.progress.model_dump() if job.progress else None
            }
            jobs_info.append(job_info)
        
        return {
            "active_jobs": jobs_info,
            "count": len(jobs_info)
        }
        
    except Exception as e:
        logger.error(f"Failed to get active jobs: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get active jobs: {str(e)}"
        )


@router.get("/jobs/recent")
async def get_recent_jobs(limit: int = Query(10, ge=1, le=100)):
    """Get recent download jobs."""
    try:
        queue_manager = get_job_queue_manager()
        recent_jobs = await queue_manager.queue.get_completed_jobs(limit=limit)
        
        # Convert to simple format
        jobs_info = []
        for job in recent_jobs:
            job_info = {
                "job_id": job.id,
                "status": job.status.value,
                "url": str(job.request.url),
                "created_at": job.created_at.isoformat(),
                "updated_at": job.updated_at.isoformat(),
                "error_message": job.error_message
            }
            
            if job.result:
                job_info["result"] = {
                    "file_path": job.result.get("file_path"),
                    "file_size": job.result.get("file_size"),
                    "processing_time": job.result.get("processing_time")
                }
            
            jobs_info.append(job_info)
        
        return {
            "recent_jobs": jobs_info,
            "count": len(jobs_info)
        }
        
    except Exception as e:
        logger.error(f"Failed to get recent jobs: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get recent jobs: {str(e)}"
        )

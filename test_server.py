#!/usr/bin/env python3
"""
Simple test server to verify our setup works.
"""

import sys
from pathlib import Path

# Add our package to the path
sys.path.append(str(Path(__file__).parent / "src" / "python"))

from fastapi import FastAPI
from video_downloader.api.models import DownloadRequest, DownloadResponse, Platform

# Create FastAPI app
app = FastAPI(
    title="Video Downloader Test Server",
    description="Test server to verify our setup",
    version="1.0.0"
)

@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "Video Downloader Service Test Server",
        "status": "running",
        "endpoints": {
            "health": "/health",
            "test_download": "/test-download"
        }
    }

@app.get("/health")
async def health():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "message": "Test server is running successfully!"
    }

@app.post("/test-download")
async def test_download(request: DownloadRequest):
    """Test download endpoint."""
    return DownloadResponse(
        job_id="test-job-123",
        status="pending",
        message=f"Test download request received for {request.url}",
        estimated_time=30,
        queue_position=1
    )

@app.get("/platforms")
async def get_platforms():
    """Get supported platforms."""
    return {
        "supported_platforms": [platform.value for platform in Platform],
        "message": "These are the platforms we support"
    }

if __name__ == "__main__":
    import uvicorn
    print("🚀 Starting test server...")
    print("📍 Server will be available at: http://localhost:8000")
    print("📖 API docs will be available at: http://localhost:8000/docs")
    print("🔍 Health check: http://localhost:8000/health")
    print("\nPress Ctrl+C to stop the server")
    
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info"
    )

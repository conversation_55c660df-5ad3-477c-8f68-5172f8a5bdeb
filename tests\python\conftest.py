"""
Pytest configuration and fixtures.
"""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock

from video_downloader.utils.config import Config


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def temp_dir():
    """Create a temporary directory for tests."""
    temp_path = tempfile.mkdtemp()
    yield Path(temp_path)
    shutil.rmtree(temp_path)


@pytest.fixture
def mock_config(temp_dir):
    """Create a mock configuration for testing."""
    config = Mock(spec=Config)
    
    # Service settings
    config.service.name = "test-video-downloader"
    config.service.version = "1.0.0"
    config.service.host = "localhost"
    config.service.port = 8080
    config.service.max_concurrent_downloads = 2
    config.service.daemon_mode = False
    
    # Storage settings
    config.storage.download_dir = str(temp_dir / "downloads")
    config.storage.temp_dir = str(temp_dir / "temp")
    config.storage.logs_dir = str(temp_dir / "logs")
    config.storage.max_file_size = "1GB"
    config.storage.cleanup_temp_files = True
    config.storage.organize_by_platform = True
    config.storage.organize_by_date = False
    
    # Queue settings
    config.queue.backend = "memory"
    config.queue.max_retries = 3
    config.queue.retry_delay = 60
    
    # Platform settings
    config.platforms = {
        "youtube": Mock(enabled=True, max_quality="4K", remove_watermark=False),
        "tiktok": Mock(enabled=True, max_quality="1080p", remove_watermark=True),
        "facebook": Mock(enabled=True, max_quality="1080p", remove_watermark=False),
    }
    
    # Processing settings
    config.processing.use_cpp_engine = False
    config.processing.ffmpeg_path = "ffmpeg"
    config.processing.concurrent_conversions = 1
    
    # Logging settings
    config.logging.level = "INFO"
    config.logging.console_logging = True
    config.logging.file_logging = False
    config.logging.structured_logging = False
    
    # API settings
    config.api.cors_enabled = True
    config.api.rate_limit = "100/minute"
    
    # Security settings
    config.security.api_key_required = False
    
    # Development settings
    config.development.debug = True
    config.development.reload = False
    
    # Helper methods
    config.get_platform_config = lambda platform: config.platforms.get(platform)
    config.is_platform_enabled = lambda platform: config.platforms.get(platform, Mock(enabled=False)).enabled
    
    # Create directories
    Path(config.storage.download_dir).mkdir(parents=True, exist_ok=True)
    Path(config.storage.temp_dir).mkdir(parents=True, exist_ok=True)
    Path(config.storage.logs_dir).mkdir(parents=True, exist_ok=True)
    
    return config


@pytest.fixture
def sample_video_metadata():
    """Create sample video metadata for testing."""
    from video_downloader.api.models import VideoMetadata, Platform
    from datetime import datetime
    
    return VideoMetadata(
        title="Test Video",
        description="This is a test video",
        duration=120,
        uploader="Test Channel",
        upload_date=datetime(2023, 12, 1),
        view_count=1000,
        like_count=50,
        thumbnail_url="https://example.com/thumbnail.jpg",
        platform=Platform.YOUTUBE,
        original_url="https://www.youtube.com/watch?v=test123",
        video_id="test123",
        tags=["test", "video"],
        categories=["Entertainment"]
    )


@pytest.fixture
def sample_download_request():
    """Create sample download request for testing."""
    from video_downloader.api.models import (
        DownloadRequest, VideoQuality, AudioFormat, VideoFormat
    )
    
    return DownloadRequest(
        url="https://www.youtube.com/watch?v=test123",
        quality=VideoQuality.Q1080P,
        audio_only=False,
        audio_format=AudioFormat.MP3,
        video_format=VideoFormat.MP4,
        extract_subtitles=False,
        extract_thumbnails=True,
        remove_watermark=False
    )


@pytest.fixture
def sample_job(sample_download_request):
    """Create sample job for testing."""
    from video_downloader.queue.job_queue import Job
    from video_downloader.api.models import JobStatus
    from datetime import datetime
    
    return Job(
        id="test-job-123",
        request=sample_download_request,
        status=JobStatus.PENDING,
        created_at=datetime.now(),
        updated_at=datetime.now(),
        priority=0,
        retry_count=0,
        max_retries=3
    )


@pytest.fixture
def mock_yt_dlp():
    """Mock yt-dlp for testing."""
    with pytest.mock.patch('yt_dlp.YoutubeDL') as mock:
        # Configure mock to return sample data
        mock_instance = Mock()
        mock.return_value.__enter__.return_value = mock_instance
        
        mock_instance.extract_info.return_value = {
            'title': 'Test Video',
            'description': 'Test description',
            'duration': 120,
            'uploader': 'Test Channel',
            'upload_date': '20231201',
            'view_count': 1000,
            'like_count': 50,
            'thumbnail': 'https://example.com/thumb.jpg',
            'id': 'test123',
            'tags': ['test', 'video'],
            'categories': ['Entertainment'],
            'formats': [
                {
                    'format_id': '720p',
                    'height': 720,
                    'vcodec': 'avc1',
                    'acodec': 'mp4a'
                },
                {
                    'format_id': '1080p',
                    'height': 1080,
                    'vcodec': 'avc1',
                    'acodec': 'mp4a'
                }
            ]
        }
        
        mock_instance.download.return_value = None
        
        yield mock


@pytest.fixture
def mock_ffmpeg():
    """Mock FFmpeg for testing."""
    with pytest.mock.patch('subprocess.run') as mock:
        mock.return_value.returncode = 0
        mock.return_value.stdout = b"FFmpeg output"
        mock.return_value.stderr = b""
        yield mock


# Pytest markers
def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line(
        "markers", "integration: mark test as integration test"
    )
    config.addinivalue_line(
        "markers", "slow: mark test as slow running"
    )
    config.addinivalue_line(
        "markers", "network: mark test as requiring network access"
    )


# Skip integration tests by default
def pytest_collection_modifyitems(config, items):
    """Modify test collection to skip integration tests by default."""
    if config.getoption("--integration"):
        return
    
    skip_integration = pytest.mark.skip(reason="need --integration option to run")
    for item in items:
        if "integration" in item.keywords:
            item.add_marker(skip_integration)


def pytest_addoption(parser):
    """Add command line options."""
    parser.addoption(
        "--integration",
        action="store_true",
        default=False,
        help="run integration tests"
    )
    parser.addoption(
        "--network",
        action="store_true", 
        default=False,
        help="run tests that require network access"
    )

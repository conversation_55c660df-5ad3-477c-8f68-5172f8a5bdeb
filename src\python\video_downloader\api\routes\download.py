"""
Download API routes.
"""

import logging
from typing import List
from fastapi import APIRouter, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse

from ..models import (
    DownloadRequest, DownloadResponse, BatchDownloadRequest, 
    BatchDownloadResponse, ErrorResponse
)
from ...queue.job_queue import get_job_queue_manager
from ...downloaders.factory import get_downloader_factory
from ...utils.config import get_config


logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/download", response_model=DownloadResponse)
async def submit_download(request: DownloadRequest):
    """Submit a video download request."""
    try:
        # Validate request
        factory = get_downloader_factory()
        if not factory.validate_request(request):
            raise HTTPException(
                status_code=400,
                detail=f"URL not supported or platform disabled: {request.url}"
            )
        
        # Submit job to queue
        queue_manager = get_job_queue_manager()
        job_id = await queue_manager.submit_job(request)
        
        # Get queue stats for position estimation
        stats = await queue_manager.get_queue_stats()
        queue_position = stats.get("queue_size", 0)
        
        # Estimate completion time (rough calculation)
        estimated_time = queue_position * 30  # 30 seconds per job estimate
        
        logger.info(f"Download job {job_id} submitted for URL: {request.url}")
        
        return DownloadResponse(
            job_id=job_id,
            status="pending",
            message="Download request submitted successfully",
            estimated_time=estimated_time if estimated_time > 0 else None,
            queue_position=queue_position if queue_position > 0 else None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to submit download request: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to submit download request: {str(e)}"
        )


@router.post("/download/batch", response_model=BatchDownloadResponse)
async def submit_batch_download(request: BatchDownloadRequest):
    """Submit multiple video download requests."""
    try:
        if len(request.urls) > 100:
            raise HTTPException(
                status_code=400,
                detail="Maximum 100 URLs allowed per batch request"
            )
        
        factory = get_downloader_factory()
        queue_manager = get_job_queue_manager()
        
        job_ids = []
        failed_urls = []
        
        for url in request.urls:
            try:
                # Create individual download request
                download_request = DownloadRequest(
                    url=url,
                    quality=request.quality,
                    audio_only=request.audio_only,
                    audio_format=request.audio_format,
                    video_format=request.video_format,
                    extract_subtitles=request.extract_subtitles,
                    extract_thumbnails=request.extract_thumbnails,
                    remove_watermark=request.remove_watermark,
                    output_dir=request.output_dir
                )
                
                # Validate request
                if not factory.validate_request(download_request):
                    failed_urls.append(str(url))
                    continue
                
                # Submit job
                job_id = await queue_manager.submit_job(download_request)
                job_ids.append(job_id)
                
            except Exception as e:
                logger.warning(f"Failed to submit URL {url}: {e}")
                failed_urls.append(str(url))
        
        if not job_ids:
            raise HTTPException(
                status_code=400,
                detail="No valid URLs could be processed"
            )
        
        # Generate batch ID
        import uuid
        batch_id = str(uuid.uuid4())
        
        # Estimate total time
        estimated_total_time = len(job_ids) * 30  # 30 seconds per job
        
        message = f"Batch download submitted: {len(job_ids)} jobs created"
        if failed_urls:
            message += f", {len(failed_urls)} URLs failed validation"
        
        logger.info(f"Batch download {batch_id} submitted: {len(job_ids)} jobs")
        
        return BatchDownloadResponse(
            batch_id=batch_id,
            job_ids=job_ids,
            total_jobs=len(job_ids),
            message=message,
            estimated_total_time=estimated_total_time
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to submit batch download: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to submit batch download: {str(e)}"
        )


@router.delete("/download/{job_id}")
async def cancel_download(job_id: str):
    """Cancel a download job."""
    try:
        queue_manager = get_job_queue_manager()
        success = await queue_manager.cancel_job(job_id)
        
        if not success:
            raise HTTPException(
                status_code=404,
                detail=f"Job {job_id} not found or cannot be cancelled"
            )
        
        logger.info(f"Download job {job_id} cancelled")
        
        return {"message": f"Job {job_id} cancelled successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to cancel job {job_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to cancel job: {str(e)}"
        )


@router.post("/download/{job_id}/retry")
async def retry_download(job_id: str):
    """Retry a failed download job."""
    try:
        queue_manager = get_job_queue_manager()
        success = await queue_manager.retry_job(job_id)
        
        if not success:
            raise HTTPException(
                status_code=404,
                detail=f"Job {job_id} not found or cannot be retried"
            )
        
        logger.info(f"Download job {job_id} scheduled for retry")
        
        return {"message": f"Job {job_id} scheduled for retry"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to retry job {job_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retry job: {str(e)}"
        )


@router.get("/download/queue/stats")
async def get_queue_stats():
    """Get download queue statistics."""
    try:
        queue_manager = get_job_queue_manager()
        stats = await queue_manager.get_queue_stats()
        
        return {
            "queue_size": stats.get("queue_size", 0),
            "active_jobs": stats.get("active_jobs", 0),
            "active_job_ids": stats.get("active_job_ids", [])
        }
        
    except Exception as e:
        logger.error(f"Failed to get queue stats: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get queue stats: {str(e)}"
        )


@router.get("/download/platforms")
async def get_supported_platforms():
    """Get supported platforms and their capabilities."""
    try:
        factory = get_downloader_factory()
        platforms = factory.get_supported_platforms()
        
        platform_info = {}
        for platform in platforms:
            capabilities = factory.get_platform_capabilities(platform)
            platform_info[platform.value] = capabilities
        
        return {
            "supported_platforms": [p.value for p in platforms],
            "platform_capabilities": platform_info
        }
        
    except Exception as e:
        logger.error(f"Failed to get platform info: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get platform info: {str(e)}"
        )
